const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    console.log(`Requête reçue: ${req.method} ${req.url}`);

    // Servir le fichier demo.html pour toutes les requêtes
    const filePath = path.join(__dirname, 'demo.html');

    fs.readFile(filePath, 'utf8', (err, content) => {
        if (err) {
            console.error('Erreur lecture fichier:', err);
            res.writeHead(500, { 'Content-Type': 'text/plain; charset=utf-8' });
            res.end('Erreur serveur: ' + err.message);
            return;
        }

        res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
        res.end(content);
    });
});

const PORT = 3000;
server.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 Serveur de démonstration lancé sur http://localhost:${PORT}`);
    console.log('📱 Le site RBA Kerry est maintenant accessible !');
    console.log('⏹️  Appuyez sur Ctrl+C pour arrêter le serveur');

    // Ouvrir automatiquement le navigateur
    const { exec } = require('child_process');
    exec(`start http://localhost:${PORT}`);
});
