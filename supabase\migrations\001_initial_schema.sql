-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create products table
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    image_url TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN ('gateaux', 'pain', 'sables', 'viennoiseries', 'autres')),
    available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create orders table
CREATE TABLE orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(50) NOT NULL,
    customer_address TEXT NOT NULL,
    delivery_date DATE NOT NULL,
    products JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled')),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create reviews table
CREATE TABLE reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT NOT NULL,
    approved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contacts table
CREATE TABLE contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
INSERT INTO products (name, description, price, category, available) VALUES
('Gâteau au Chocolat', 'Délicieux gâteau au chocolat noir avec ganache maison', 25.00, 'gateaux', true),
('Pain de Campagne', 'Pain artisanal au levain naturel, croûte dorée et mie moelleuse', 4.50, 'pain', true),
('Sablés Vanille', 'Sablés fondants à la vanille de Madagascar, par 12 pièces', 8.00, 'sables', true),
('Croissant Beurre', 'Croissant pur beurre, feuilletage croustillant', 1.80, 'viennoiseries', true),
('Tarte aux Pommes', 'Tarte aux pommes fraîches sur pâte brisée maison', 18.00, 'gateaux', true),
('Baguette Tradition', 'Baguette de tradition française, farine Label Rouge', 1.20, 'pain', true);

INSERT INTO reviews (customer_name, rating, comment, approved) VALUES
('Marie Dubois', 5, 'Excellente boulangerie ! Les gâteaux sont délicieux et le service est parfait.', true),
('Pierre Martin', 5, 'Le pain de campagne est exceptionnel, je recommande vivement !', true),
('Sophie Laurent', 4, 'Très bons produits, livraison rapide. Juste un petit bémol sur les horaires.', true);

INSERT INTO contacts (name, email, subject, message) VALUES
('Jean Dupont', '<EMAIL>', 'Commande spéciale', 'Bonjour, je souhaiterais commander un gâteau personnalisé pour un anniversaire.');

-- Enable Row Level Security (RLS)
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE contacts ENABLE ROW LEVEL SECURITY;

-- Create policies for public access (adjust as needed for your security requirements)
CREATE POLICY "Products are viewable by everyone" ON products FOR SELECT USING (true);
CREATE POLICY "Reviews are viewable by everyone" ON reviews FOR SELECT USING (approved = true);

-- Allow inserts for orders and contacts (from public users)
CREATE POLICY "Anyone can insert orders" ON orders FOR INSERT WITH CHECK (true);
CREATE POLICY "Anyone can insert contacts" ON contacts FOR INSERT WITH CHECK (true);

-- Admin policies (you'll need to set up authentication for these)
-- For now, we'll allow all operations (you should restrict this in production)
CREATE POLICY "Admin can do everything on products" ON products FOR ALL USING (true);
CREATE POLICY "Admin can do everything on orders" ON orders FOR ALL USING (true);
CREATE POLICY "Admin can do everything on reviews" ON reviews FOR ALL USING (true);
CREATE POLICY "Admin can do everything on contacts" ON contacts FOR ALL USING (true);
