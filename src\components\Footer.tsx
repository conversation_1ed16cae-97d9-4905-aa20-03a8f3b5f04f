'use client'

import Link from 'next/link'
import { MapPin, Phone, Mail, Clock, Facebook, Instagram } from 'lucide-react'
import { translations } from '@/lib/translations'
import { useState } from 'react'

export function Footer() {
  const [language] = useState<'fr' | 'en'>('fr')
  const t = translations[language]

  return (
    <footer className="bg-chocolate-800 text-cream-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo et description */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="font-handwriting text-3xl font-bold text-cream-100 mb-4">
              RBA Kerry
            </h3>
            <p className="text-cream-200 mb-6 max-w-md">
              {t.footer.description}
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-cream-300 hover:text-cream-100 transition-colors">
                <Facebook className="h-6 w-6" />
              </a>
              <a href="#" className="text-cream-300 hover:text-cream-100 transition-colors">
                <Instagram className="h-6 w-6" />
              </a>
            </div>
          </div>

          {/* Liens rapides */}
          <div>
            <h4 className="font-serif text-lg font-semibold text-cream-100 mb-4">
              {t.footer.links}
            </h4>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-cream-300 hover:text-cream-100 transition-colors">
                  {t.nav.home}
                </Link>
              </li>
              <li>
                <Link href="/products" className="text-cream-300 hover:text-cream-100 transition-colors">
                  {t.nav.products}
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-cream-300 hover:text-cream-100 transition-colors">
                  {t.nav.contact}
                </Link>
              </li>
            </ul>
          </div>

          {/* Informations de contact */}
          <div>
            <h4 className="font-serif text-lg font-semibold text-cream-100 mb-4">
              {t.footer.contact}
            </h4>
            <ul className="space-y-3">
              <li className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-cream-300 mt-0.5 flex-shrink-0" />
                <span className="text-cream-300 text-sm">
                  123 Rue de la Boulangerie<br />
                  75001 Paris, France
                </span>
              </li>
              <li className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-cream-300 flex-shrink-0" />
                <span className="text-cream-300 text-sm">+33 1 23 45 67 89</span>
              </li>
              <li className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-cream-300 flex-shrink-0" />
                <span className="text-cream-300 text-sm"><EMAIL></span>
              </li>
              <li className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-cream-300 mt-0.5 flex-shrink-0" />
                <div className="text-cream-300 text-sm">
                  <div>Lun-Ven: 7h-19h</div>
                  <div>Sam: 7h-18h</div>
                  <div>Dim: 8h-13h</div>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-chocolate-700 mt-8 pt-8 text-center">
          <p className="text-cream-400 text-sm">
            © 2024 RBA Kerry. {t.footer.rights}
          </p>
        </div>
      </div>
    </footer>
  )
}
