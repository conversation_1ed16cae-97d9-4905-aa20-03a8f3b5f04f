'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { MapPin, Phone, Mail, Clock, Send } from 'lucide-react'
import { translations } from '@/lib/translations'
import { supabase, type Contact } from '@/lib/supabase'

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
}

export default function ContactPage() {
  const [language] = useState<'fr' | 'en'>('fr')
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const t = translations[language]

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm<ContactFormData>()

  const onSubmit = async (data: ContactFormData) => {
    setLoading(true)
    try {
      const contact: Omit<Contact, 'id' | 'created_at'> = {
        name: data.name,
        email: data.email,
        subject: data.subject,
        message: data.message
      }

      const { error } = await supabase
        .from('contacts')
        .insert([contact])

      if (error) {
        throw error
      }

      setSuccess(true)
      reset()
      
      // Masquer le message de succès après 5 secondes
      setTimeout(() => {
        setSuccess(false)
      }, 5000)

    } catch (error) {
      console.error('Erreur lors de l\'envoi du message:', error)
      alert('Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-chocolate-600 to-chocolate-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="font-handwriting text-5xl md:text-6xl font-bold mb-4">
            {t.contact.title}
          </h1>
          <p className="text-xl text-cream-200 max-w-2xl mx-auto">
            Nous sommes là pour vous aider ! N'hésitez pas à nous contacter pour toute question ou commande spéciale.
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Informations de contact */}
          <div>
            <h2 className="font-serif text-3xl font-bold text-chocolate-800 mb-8">
              Nos Coordonnées
            </h2>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="bg-rose-100 rounded-full p-3">
                  <MapPin className="h-6 w-6 text-rose-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-chocolate-800 mb-1">{t.contact.info.address}</h3>
                  <p className="text-chocolate-600">
                    123 Rue de la Boulangerie<br />
                    75001 Paris, France
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-cream-200 rounded-full p-3">
                  <Phone className="h-6 w-6 text-chocolate-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-chocolate-800 mb-1">{t.contact.info.phone}</h3>
                  <p className="text-chocolate-600">+33 1 23 45 67 89</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-chocolate-200 rounded-full p-3">
                  <Mail className="h-6 w-6 text-chocolate-700" />
                </div>
                <div>
                  <h3 className="font-semibold text-chocolate-800 mb-1">{t.contact.info.email}</h3>
                  <p className="text-chocolate-600"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="bg-rose-200 rounded-full p-3">
                  <Clock className="h-6 w-6 text-rose-700" />
                </div>
                <div>
                  <h3 className="font-semibold text-chocolate-800 mb-1">{t.contact.info.hours}</h3>
                  <div className="text-chocolate-600 space-y-1">
                    <p>Lundi - Vendredi: 7h00 - 19h00</p>
                    <p>Samedi: 7h00 - 18h00</p>
                    <p>Dimanche: 8h00 - 13h00</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Carte (placeholder) */}
            <div className="mt-8">
              <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                Nous Trouver
              </h3>
              <div className="bg-gradient-to-br from-cream-200 to-rose-100 rounded-lg h-64 flex items-center justify-center">
                <p className="text-chocolate-600 text-center">
                  <MapPin className="h-12 w-12 mx-auto mb-2 text-chocolate-400" />
                  Carte interactive<br />
                  (Intégration Google Maps)
                </p>
              </div>
            </div>
          </div>

          {/* Formulaire de contact */}
          <div>
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="font-serif text-3xl font-bold text-chocolate-800 mb-6">
                Envoyez-nous un message
              </h2>

              {success && (
                <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                  <div className="flex items-center">
                    <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Votre message a été envoyé avec succès ! Nous vous répondrons bientôt.
                  </div>
                </div>
              )}

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-chocolate-700 mb-2">
                      {t.contact.form.name} *
                    </label>
                    <input
                      type="text"
                      {...register('name', { required: 'Le nom est requis' })}
                      className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                      placeholder="Votre nom"
                    />
                    {errors.name && (
                      <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-chocolate-700 mb-2">
                      {t.contact.form.email} *
                    </label>
                    <input
                      type="email"
                      {...register('email', { 
                        required: 'L\'email est requis',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Email invalide'
                        }
                      })}
                      className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-chocolate-700 mb-2">
                    {t.contact.form.subject} *
                  </label>
                  <input
                    type="text"
                    {...register('subject', { required: 'Le sujet est requis' })}
                    className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                    placeholder="Sujet de votre message"
                  />
                  {errors.subject && (
                    <p className="text-red-500 text-sm mt-1">{errors.subject.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-chocolate-700 mb-2">
                    {t.contact.form.message} *
                  </label>
                  <textarea
                    {...register('message', { required: 'Le message est requis' })}
                    rows={6}
                    className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                    placeholder="Votre message..."
                  />
                  {errors.message && (
                    <p className="text-red-500 text-sm mt-1">{errors.message.message}</p>
                  )}
                </div>

                <button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-rose-500 hover:bg-rose-600 disabled:bg-rose-300 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg disabled:transform-none flex items-center justify-center"
                >
                  {loading ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                      Envoi en cours...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Send className="h-5 w-5 mr-2" />
                      {t.contact.form.submit}
                    </div>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
