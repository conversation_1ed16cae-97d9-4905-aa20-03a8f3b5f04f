import type { Config } from 'tailwindcss'

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        cream: {
          50: '#fefdf8',
          100: '#fdf9e7',
          200: '#faf0c4',
          300: '#f6e397',
          400: '#f0d168',
          500: '#ebc444',
          600: '#d4a72c',
          700: '#b08426',
          800: '#8f6827',
          900: '#775624',
        },
        chocolate: {
          50: '#faf7f0',
          100: '#f4ede0',
          200: '#e8d9c0',
          300: '#d9c19a',
          400: '#c8a472',
          500: '#bc9158',
          600: '#a67c4c',
          700: '#8a6441',
          800: '#70523a',
          900: '#5c4430',
        },
        rose: {
          50: '#fdf2f8',
          100: '#fce7f3',
          200: '#fbcfe8',
          300: '#f9a8d4',
          400: '#f472b6',
          500: '#ec4899',
          600: '#db2777',
          700: '#be185d',
          800: '#9d174d',
          900: '#831843',
        }
      },
      fontFamily: {
        'handwriting': ['Dancing Script', 'cursive'],
        'serif': ['Playfair Display', 'serif'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
    },
  },
  plugins: [],
}
export default config
