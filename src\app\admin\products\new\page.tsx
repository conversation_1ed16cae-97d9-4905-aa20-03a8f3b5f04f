'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import Link from 'next/link'
import { ArrowLeft, Save, Upload } from 'lucide-react'
import { supabase, type Product } from '@/lib/supabase'

interface ProductFormData {
  name: string
  description: string
  price: number
  category: 'gateaux' | 'pain' | 'sables' | 'viennoiseries' | 'autres'
  image_url?: string
  available: boolean
}

export default function NewProductPage() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [imagePreview, setImagePreview] = useState<string>('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<ProductFormData>({
    defaultValues: {
      available: true
    }
  })

  const onSubmit = async (data: ProductFormData) => {
    setLoading(true)
    try {
      const product: Omit<Product, 'id' | 'created_at' | 'updated_at'> = {
        name: data.name,
        description: data.description,
        price: data.price,
        category: data.category,
        image_url: data.image_url || '',
        available: data.available
      }

      const { error } = await supabase
        .from('products')
        .insert([product])

      if (error) {
        throw error
      }

      router.push('/admin/products')
    } catch (error) {
      console.error('Erreur lors de la création du produit:', error)
      alert('Une erreur est survenue lors de la création du produit.')
    } finally {
      setLoading(false)
    }
  }

  const handleImageUrlChange = (url: string) => {
    setValue('image_url', url)
    setImagePreview(url)
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4">
            <Link
              href="/admin/products"
              className="text-chocolate-600 hover:text-chocolate-800 transition-colors"
            >
              <ArrowLeft className="h-6 w-6" />
            </Link>
            <div>
              <h1 className="font-handwriting text-4xl font-bold text-chocolate-800">
                Nouveau Produit
              </h1>
              <p className="text-chocolate-600 mt-2">
                Ajoutez un nouveau produit à votre catalogue
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-8">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Informations de base */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-chocolate-700 mb-2">
                  Nom du produit *
                </label>
                <input
                  type="text"
                  {...register('name', { required: 'Le nom est requis' })}
                  className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                  placeholder="Ex: Gâteau au chocolat"
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-chocolate-700 mb-2">
                  Catégorie *
                </label>
                <select
                  {...register('category', { required: 'La catégorie est requise' })}
                  className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                >
                  <option value="">Sélectionnez une catégorie</option>
                  <option value="gateaux">Gâteaux</option>
                  <option value="pain">Pains</option>
                  <option value="sables">Sablés</option>
                  <option value="viennoiseries">Viennoiseries</option>
                  <option value="autres">Autres</option>
                </select>
                {errors.category && (
                  <p className="text-red-500 text-sm mt-1">{errors.category.message}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-chocolate-700 mb-2">
                Description *
              </label>
              <textarea
                {...register('description', { required: 'La description est requise' })}
                rows={4}
                className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                placeholder="Décrivez votre produit..."
              />
              {errors.description && (
                <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
              )}
            </div>

            {/* Prix et disponibilité */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-chocolate-700 mb-2">
                  Prix (€) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('price', { 
                    required: 'Le prix est requis',
                    min: { value: 0, message: 'Le prix doit être positif' }
                  })}
                  className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                  placeholder="0.00"
                />
                {errors.price && (
                  <p className="text-red-500 text-sm mt-1">{errors.price.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-chocolate-700 mb-2">
                  Disponibilité
                </label>
                <div className="flex items-center space-x-3 mt-3">
                  <input
                    type="checkbox"
                    {...register('available')}
                    className="h-4 w-4 text-rose-600 focus:ring-rose-500 border-chocolate-300 rounded"
                  />
                  <span className="text-chocolate-700">Produit disponible</span>
                </div>
              </div>
            </div>

            {/* Image */}
            <div>
              <label className="block text-sm font-medium text-chocolate-700 mb-2">
                URL de l'image
              </label>
              <div className="space-y-4">
                <input
                  type="url"
                  {...register('image_url')}
                  onChange={(e) => handleImageUrlChange(e.target.value)}
                  className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                  placeholder="https://exemple.com/image.jpg"
                />
                
                {imagePreview && (
                  <div className="mt-4">
                    <p className="text-sm text-chocolate-600 mb-2">Aperçu de l'image:</p>
                    <div className="w-32 h-32 rounded-lg overflow-hidden border border-chocolate-200">
                      <img
                        src={imagePreview}
                        alt="Aperçu"
                        className="w-full h-full object-cover"
                        onError={() => setImagePreview('')}
                      />
                    </div>
                  </div>
                )}
                
                <div className="text-sm text-chocolate-500">
                  <p>Conseils pour l'image:</p>
                  <ul className="list-disc list-inside space-y-1 mt-1">
                    <li>Format recommandé: JPG ou PNG</li>
                    <li>Taille recommandée: 400x400 pixels minimum</li>
                    <li>Poids maximum: 2MB</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Boutons d'action */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-chocolate-200">
              <Link
                href="/admin/products"
                className="px-4 py-2 text-chocolate-600 hover:text-chocolate-800 transition-colors"
              >
                Annuler
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="bg-rose-500 hover:bg-rose-600 disabled:bg-rose-300 text-white font-semibold py-2 px-6 rounded-lg transition-colors flex items-center space-x-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                <span>{loading ? 'Création...' : 'Créer le produit'}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
