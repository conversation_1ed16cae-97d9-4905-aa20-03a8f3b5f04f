{% extends 'base.html' %}

{% block title %}Contact - RBA Kerry{% endblock %}

{% block content %}
<div class="min-h-screen bg-cream-50">
    <!-- Header -->
    <div class="bg-gradient-to-r from-chocolate-600 to-chocolate-800 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="font-handwriting text-5xl md:text-6xl font-bold mb-4">
                Nous Contacter
            </h1>
            <p class="text-xl text-cream-200 max-w-2xl mx-auto">
                Nous sommes là pour vous aider ! N'hésitez pas à nous contacter pour toute question ou commande spéciale.
            </p>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Informations de contact -->
            <div>
                <h2 class="font-serif text-3xl font-bold text-chocolate-800 mb-8">
                    Nos Coordonnées
                </h2>
                
                <div class="space-y-6">
                    <div class="flex items-start space-x-4">
                        <div class="bg-rose-100 rounded-full p-3">
                            <svg class="h-6 w-6 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-chocolate-800 mb-1">Adresse</h3>
                            <p class="text-chocolate-600">
                                123 Rue de la Boulangerie<br />
                                75001 Paris, France
                            </p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="bg-cream-200 rounded-full p-3">
                            <svg class="h-6 w-6 text-chocolate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-chocolate-800 mb-1">Téléphone</h3>
                            <p class="text-chocolate-600">+33 1 23 45 67 89</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="bg-chocolate-200 rounded-full p-3">
                            <svg class="h-6 w-6 text-chocolate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-chocolate-800 mb-1">Email</h3>
                            <p class="text-chocolate-600"><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4">
                        <div class="bg-rose-200 rounded-full p-3">
                            <svg class="h-6 w-6 text-rose-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                        <div>
                            <h3 class="font-semibold text-chocolate-800 mb-1">Horaires</h3>
                            <div class="text-chocolate-600 space-y-1">
                                <p>Lundi - Vendredi: 7h00 - 19h00</p>
                                <p>Samedi: 7h00 - 18h00</p>
                                <p>Dimanche: 8h00 - 13h00</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Carte (placeholder) -->
                <div class="mt-8">
                    <h3 class="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                        Nous Trouver
                    </h3>
                    <div class="bg-gradient-to-br from-cream-200 to-rose-100 rounded-lg h-64 flex items-center justify-center">
                        <p class="text-chocolate-600 text-center">
                            <svg class="h-12 w-12 mx-auto mb-2 text-chocolate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Carte interactive<br />
                            (Intégration Google Maps)
                        </p>
                    </div>
                </div>
            </div>

            <!-- Formulaire de contact -->
            <div>
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="font-serif text-3xl font-bold text-chocolate-800 mb-6">
                        Envoyez-nous un message
                    </h2>

                    <form method="POST" class="space-y-6">
                        {% csrf_token %}
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                    {{ form.name.label }} *
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <p class="text-red-500 text-sm mt-1">{{ form.name.errors.0 }}</p>
                                {% endif %}
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                    {{ form.email.label }} *
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <p class="text-red-500 text-sm mt-1">{{ form.email.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                {{ form.subject.label }} *
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.subject.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                {{ form.message.label }} *
                            </label>
                            {{ form.message }}
                            {% if form.message.errors %}
                                <p class="text-red-500 text-sm mt-1">{{ form.message.errors.0 }}</p>
                            {% endif %}
                        </div>

                        <button type="submit" class="w-full bg-rose-500 hover:bg-rose-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                            Envoyer le message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
