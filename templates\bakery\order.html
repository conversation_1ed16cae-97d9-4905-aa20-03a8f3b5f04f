{% extends 'base.html' %}

{% block title %}Passer une commande - RBA Kerry{% endblock %}

{% block content %}
<div class="min-h-screen bg-cream-50 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h1 class="font-handwriting text-4xl md:text-5xl font-bold text-chocolate-800 mb-4">
                Passer une commande
            </h1>
            <p class="text-chocolate-600 text-lg">
                Complétez vos informations pour finaliser votre commande
            </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Résumé de commande -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6 sticky top-8">
                    <h3 class="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                        R<PERSON>um<PERSON> de commande
                    </h3>
                    
                    <div class="border-b border-chocolate-200 pb-4 mb-4">
                        <div class="flex justify-between items-start mb-2">
                            <span class="font-medium text-chocolate-800">{{ product.name }}</span>
                        </div>
                        <div class="flex justify-between text-sm text-chocolate-600">
                            <span>Quantité: {{ product_data.quantity }}</span>
                            <span>{{ product_data.unit_price }}€ / unité</span>
                        </div>
                    </div>
                    
                    <div class="flex justify-between items-center text-lg font-bold text-chocolate-800">
                        <span>Total:</span>
                        <span>{{ product_data.total_price }}€</span>
                    </div>
                    
                    <div class="mt-4 p-3 bg-rose-50 rounded-lg">
                        <p class="text-sm text-rose-800">
                            <svg class="h-4 w-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            Livraison gratuite pour les commandes de plus de 30€
                        </p>
                    </div>
                </div>
            </div>

            <!-- Formulaire de commande -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <form method="POST" class="space-y-6">
                        {% csrf_token %}
                        
                        <!-- Informations personnelles -->
                        <div>
                            <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                Informations personnelles
                            </h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                        {{ form.customer_name.label }} *
                                    </label>
                                    {{ form.customer_name }}
                                    {% if form.customer_name.errors %}
                                        <p class="text-red-500 text-sm mt-1">{{ form.customer_name.errors.0 }}</p>
                                    {% endif %}
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                        {{ form.customer_phone.label }} *
                                    </label>
                                    {{ form.customer_phone }}
                                    {% if form.customer_phone.errors %}
                                        <p class="text-red-500 text-sm mt-1">{{ form.customer_phone.errors.0 }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                    {{ form.customer_email.label }} *
                                </label>
                                {{ form.customer_email }}
                                {% if form.customer_email.errors %}
                                    <p class="text-red-500 text-sm mt-1">{{ form.customer_email.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Adresse de livraison -->
                        <div>
                            <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                Adresse de livraison
                            </h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                    {{ form.customer_address.label }} *
                                </label>
                                {{ form.customer_address }}
                                {% if form.customer_address.errors %}
                                    <p class="text-red-500 text-sm mt-1">{{ form.customer_address.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Date de livraison -->
                        <div>
                            <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                Date de livraison souhaitée
                            </h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                    {{ form.delivery_date.label }} *
                                </label>
                                <input
                                    type="date"
                                    name="delivery_date"
                                    id="delivery_date"
                                    class="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                                    required
                                >
                                {% if form.delivery_date.errors %}
                                    <p class="text-red-500 text-sm mt-1">{{ form.delivery_date.errors.0 }}</p>
                                {% endif %}
                                <p class="text-sm text-chocolate-500 mt-1">
                                    Livraison possible du lundi au samedi, dans un délai de 3 mois maximum.
                                </p>
                            </div>
                        </div>

                        <!-- Notes spéciales -->
                        <div>
                            <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                </svg>
                                Notes spéciales (optionnel)
                            </h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-chocolate-700 mb-2">
                                    {{ form.notes.label }}
                                </label>
                                {{ form.notes }}
                            </div>
                        </div>

                        <!-- Bouton de soumission -->
                        <div class="pt-6">
                            <button type="submit" class="w-full bg-rose-500 hover:bg-rose-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                                Confirmer la commande
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('delivery_date');

    // Calculer les dates min et max
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const maxDate = new Date(today);
    maxDate.setDate(today.getDate() + 90); // 3 mois

    // Formater les dates pour l'input
    const formatDate = (date) => {
        return date.toISOString().split('T')[0];
    };

    dateInput.min = formatDate(tomorrow);
    dateInput.max = formatDate(maxDate);

    // Validation pour exclure les dimanches
    dateInput.addEventListener('change', function() {
        const selectedDate = new Date(this.value);
        if (selectedDate.getDay() === 0) { // Dimanche = 0
            alert('Désolé, nous ne livrons pas le dimanche. Veuillez choisir une autre date.');
            this.value = '';
        }
    });
});
</script>
{% endblock %}
