from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q
from datetime import date, timedelta
from .models import Product, Order, OrderItem, Review, Contact
from .forms import OrderForm, ContactForm


def home(request):
    """Page d'accueil"""
    # Récupérer quelques produits en vedette
    featured_products = Product.objects.filter(available=True)[:6]
    
    # Récupérer les avis approuvés
    reviews = Review.objects.filter(approved=True)[:3]
    
    context = {
        'featured_products': featured_products,
        'reviews': reviews,
    }
    return render(request, 'bakery/home.html', context)


def products(request):
    """Page catalogue des produits"""
    # Filtres
    category = request.GET.get('category', '')
    search = request.GET.get('search', '')
    
    # Requête de base
    products_list = Product.objects.filter(available=True)
    
    # Appliquer les filtres
    if category and category != 'all':
        products_list = products_list.filter(category=category)
    
    if search:
        products_list = products_list.filter(
            Q(name__icontains=search) | Q(description__icontains=search)
        )
    
    # Pagination
    paginator = Paginator(products_list, 12)
    page_number = request.GET.get('page')
    products_page = paginator.get_page(page_number)
    
    # Catégories pour les filtres
    categories = [
        ('all', 'Tous'),
        ('gateaux', 'Gâteaux'),
        ('pain', 'Pains'),
        ('sables', 'Sablés'),
        ('viennoiseries', 'Viennoiseries'),
        ('autres', 'Autres'),
    ]
    
    context = {
        'products': products_page,
        'categories': categories,
        'current_category': category,
        'search_query': search,
    }
    return render(request, 'bakery/products.html', context)


def product_detail(request, product_id):
    """Page détail d'un produit"""
    product = get_object_or_404(Product, id=product_id, available=True)
    
    # Produits similaires
    related_products = Product.objects.filter(
        category=product.category,
        available=True
    ).exclude(id=product.id)[:3]
    
    context = {
        'product': product,
        'related_products': related_products,
    }
    return render(request, 'bakery/product_detail.html', context)


def order(request):
    """Page de commande"""
    if request.method == 'POST':
        form = OrderForm(request.POST)
        if form.is_valid():
            # Récupérer les données du produit depuis la session
            product_data = request.session.get('order_product')
            if not product_data:
                messages.error(request, 'Aucun produit sélectionné pour la commande.')
                return redirect('bakery:products')
            
            # Créer la commande
            order = form.save()
            
            # Créer l'article de commande
            product = get_object_or_404(Product, id=product_data['product_id'])
            OrderItem.objects.create(
                order=order,
                product=product,
                quantity=product_data['quantity'],
                unit_price=product.price
            )
            
            # Calculer le total
            order.total_amount = product.price * product_data['quantity']
            order.save()
            
            # Nettoyer la session
            del request.session['order_product']
            
            messages.success(request, 'Votre commande a été enregistrée avec succès!')
            return redirect('bakery:order_success')
    else:
        # Vérifier qu'il y a un produit en session
        product_data = request.session.get('order_product')
        if not product_data:
            messages.error(request, 'Veuillez d\'abord sélectionner un produit.')
            return redirect('bakery:products')
        
        form = OrderForm()
        product = get_object_or_404(Product, id=product_data['product_id'])
    
    # Générer les dates disponibles
    available_dates = []
    today = date.today()
    for i in range(1, 15):  # 14 jours à partir de demain
        delivery_date = today + timedelta(days=i)
        if delivery_date.weekday() != 6:  # Exclure les dimanches
            available_dates.append(delivery_date)
    
    context = {
        'form': form,
        'product': product,
        'product_data': product_data,
        'available_dates': available_dates,
    }
    return render(request, 'bakery/order.html', context)


def order_success(request):
    """Page de confirmation de commande"""
    return render(request, 'bakery/order_success.html')


def contact(request):
    """Page de contact"""
    if request.method == 'POST':
        form = ContactForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(request, 'Votre message a été envoyé avec succès!')
            return redirect('bakery:contact_success')
    else:
        form = ContactForm()
    
    context = {
        'form': form,
    }
    return render(request, 'bakery/contact.html', context)


def contact_success(request):
    """Page de confirmation de contact"""
    return render(request, 'bakery/contact_success.html')


def api_products(request):
    """API pour récupérer les produits (AJAX)"""
    category = request.GET.get('category', '')
    search = request.GET.get('search', '')
    
    products_list = Product.objects.filter(available=True)
    
    if category and category != 'all':
        products_list = products_list.filter(category=category)
    
    if search:
        products_list = products_list.filter(
            Q(name__icontains=search) | Q(description__icontains=search)
        )
    
    products_data = []
    for product in products_list:
        products_data.append({
            'id': product.id,
            'name': product.name,
            'description': product.description,
            'price': float(product.price),
            'category': product.category,
            'image_url': product.image.url if product.image else None,
        })
    
    return JsonResponse({'products': products_data})


def add_to_cart(request, product_id):
    """Ajouter un produit à la commande (session)"""
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id, available=True)
        quantity = int(request.POST.get('quantity', 1))

        # Stocker dans la session
        request.session['order_product'] = {
            'product_id': product.id,
            'product_name': product.name,
            'quantity': quantity,
            'unit_price': float(product.price),
            'total_price': float(product.price) * quantity,
        }

        messages.success(request, f'{product.name} ajouté à votre commande !')
        return redirect('bakery:order')

    return redirect('bakery:products')
