{% extends 'base.html' %}

{% block title %}{{ product.name }} - RBA Kerry{% endblock %}

{% block content %}
<div class="min-h-screen bg-cream-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Bouton retour -->
        <a href="{% url 'bakery:products' %}" class="inline-flex items-center text-chocolate-600 hover:text-chocolate-800 mb-8 transition-colors">
            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
            Retour aux produits
        </a>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Image du produit -->
            <div class="relative">
                <div class="aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-cream-200 to-rose-100 shadow-lg">
                    {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-full h-full object-cover">
                    {% else %}
                        <div class="flex items-center justify-center h-full">
                            <svg class="h-32 w-32 text-chocolate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                            </svg>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Détails du produit -->
            <div class="flex flex-col justify-center">
                <div class="mb-4">
                    <span class="inline-block bg-rose-100 text-rose-800 text-sm font-medium px-3 py-1 rounded-full mb-4">
                        {{ product.category.display_name }}
                    </span>
                </div>

                <h1 class="font-handwriting text-4xl md:text-5xl font-bold text-chocolate-800 mb-4">
                    {{ product.name }}
                </h1>

                <div class="flex items-center mb-6">
                    <div class="flex items-center mr-4">
                        {% for i in "12345" %}
                            <svg class="h-5 w-5 text-yellow-400 fill-current" viewBox="0 0 24 24">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        {% endfor %}
                    </div>
                    <span class="text-chocolate-600 text-sm">(4.8/5 - 24 avis)</span>
                </div>

                <p class="text-chocolate-700 text-lg mb-8 leading-relaxed">
                    {{ product.description }}
                </p>

                <div class="mb-8">
                    <span class="text-3xl font-bold text-chocolate-800">
                        {{ product.price }}€
                    </span>
                    <span class="text-chocolate-600 ml-2">par unité</span>
                </div>

                <!-- Formulaire de commande -->
                <form method="POST" action="{% url 'bakery:add_to_cart' product.id %}" class="mb-8">
                    {% csrf_token %}
                    
                    <!-- Sélecteur de quantité -->
                    <div class="mb-8">
                        <label class="block text-chocolate-800 font-medium mb-3">
                            Quantité
                        </label>
                        <div class="flex items-center space-x-4">
                            <button type="button" onclick="decreaseQuantity()" class="w-10 h-10 rounded-full bg-chocolate-200 hover:bg-chocolate-300 flex items-center justify-center transition-colors">
                                <svg class="h-4 w-4 text-chocolate-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                </svg>
                            </button>
                            <input type="number" name="quantity" id="quantity" value="1" min="1" max="10" class="text-xl font-semibold text-chocolate-800 w-16 text-center border border-chocolate-300 rounded-lg py-2">
                            <button type="button" onclick="increaseQuantity()" class="w-10 h-10 rounded-full bg-chocolate-200 hover:bg-chocolate-300 flex items-center justify-center transition-colors">
                                <svg class="h-4 w-4 text-chocolate-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Total et bouton de commande -->
                    <div class="border-t border-chocolate-200 pt-6">
                        <div class="flex items-center justify-between mb-6">
                            <span class="text-lg font-medium text-chocolate-800">Total:</span>
                            <span id="total-price" class="text-2xl font-bold text-chocolate-800">
                                {{ product.price }}€
                            </span>
                        </div>

                        <button type="submit" class="w-full bg-rose-500 hover:bg-rose-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center space-x-2">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                            </svg>
                            <span>Commander maintenant</span>
                        </button>

                        <p class="text-sm text-chocolate-600 mt-4 text-center">
                            Livraison gratuite pour les commandes de plus de 30€
                        </p>
                    </div>
                </form>
            </div>
        </div>

        <!-- Informations supplémentaires -->
        <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-white rounded-lg p-6 shadow-md text-center">
                <div class="bg-rose-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                    </svg>
                </div>
                <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-2">
                    Fait Maison
                </h3>
                <p class="text-chocolate-600 text-sm">
                    Préparé artisanalement avec des ingrédients frais et de qualité
                </p>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-md text-center">
                <div class="bg-cream-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-chocolate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                    </svg>
                </div>
                <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-2">
                    Qualité Premium
                </h3>
                <p class="text-chocolate-600 text-sm">
                    Sélection rigoureuse des meilleurs ingrédients pour un goût authentique
                </p>
            </div>

            <div class="bg-white rounded-lg p-6 shadow-md text-center">
                <div class="bg-chocolate-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <svg class="h-8 w-8 text-chocolate-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                </div>
                <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-2">
                    Livraison Rapide
                </h3>
                <p class="text-chocolate-600 text-sm">
                    Commande préparée et livrée dans les meilleurs délais
                </p>
            </div>
        </div>

        <!-- Produits similaires -->
        {% if related_products %}
        <div class="mt-16">
            <h2 class="font-serif text-3xl font-bold text-chocolate-800 mb-8 text-center">
                Vous pourriez aussi aimer
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {% for related_product in related_products %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                    <div class="h-48 bg-gradient-to-br from-cream-200 to-rose-100">
                        {% if related_product.image %}
                            <img src="{{ related_product.image.url }}" alt="{{ related_product.name }}" class="w-full h-full object-cover">
                        {% else %}
                            <div class="flex items-center justify-center h-full">
                                <svg class="h-16 w-16 text-chocolate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                </svg>
                            </div>
                        {% endif %}
                    </div>
                    <div class="p-4">
                        <h3 class="font-serif text-lg font-semibold text-chocolate-800 mb-2">{{ related_product.name }}</h3>
                        <p class="text-chocolate-600 text-sm mb-4">{{ related_product.description|truncatewords:10 }}</p>
                        <div class="flex items-center justify-between">
                            <span class="text-lg font-bold text-chocolate-800">{{ related_product.price }}€</span>
                            <a href="{% url 'bakery:product_detail' related_product.id %}" class="bg-rose-500 hover:bg-rose-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                Voir
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
    const quantityInput = document.getElementById('quantity');
    const totalPriceElement = document.getElementById('total-price');
    const unitPrice = {{ product.price }};

    function updateTotal() {
        const quantity = parseInt(quantityInput.value);
        const total = (unitPrice * quantity).toFixed(2);
        totalPriceElement.textContent = total + '€';
    }

    function increaseQuantity() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 10) {
            quantityInput.value = currentValue + 1;
            updateTotal();
        }
    }

    function decreaseQuantity() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
            updateTotal();
        }
    }

    quantityInput.addEventListener('input', updateTotal);
</script>
{% endblock %}
