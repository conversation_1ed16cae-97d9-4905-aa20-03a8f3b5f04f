'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Filter, ShoppingCart } from 'lucide-react'
import { translations } from '@/lib/translations'
import { supabase, type Product } from '@/lib/supabase'

export default function ProductsPage() {
  const [language] = useState<'fr' | 'en'>('fr')
  const [products, setProducts] = useState<Product[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [loading, setLoading] = useState(true)
  const t = translations[language]

  const categories = [
    { key: 'all', label: t.products.filter.all },
    { key: 'gateaux', label: t.products.filter.gateaux },
    { key: 'pain', label: t.products.filter.pain },
    { key: 'sables', label: t.products.filter.sables },
    { key: 'viennoiseries', label: t.products.filter.viennoiseries },
    { key: 'autres', label: t.products.filter.autres },
  ]

  useEffect(() => {
    fetchProducts()
  }, [])

  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredProducts(products)
    } else {
      setFilteredProducts(products.filter(product => product.category === selectedCategory))
    }
  }, [selectedCategory, products])

  const fetchProducts = async () => {
    setLoading(true)
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('available', true)
      .order('created_at', { ascending: false })

    if (data && !error) {
      setProducts(data)
      setFilteredProducts(data)
    }
    setLoading(false)
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-chocolate-600 to-chocolate-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="font-handwriting text-5xl md:text-6xl font-bold mb-4">
            {t.products.title}
          </h1>
          <p className="text-xl text-cream-200 max-w-2xl mx-auto">
            Découvrez notre sélection de délices artisanaux, préparés avec amour et des ingrédients de qualité.
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Filtres */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <Filter className="h-5 w-5 text-chocolate-600 mr-2" />
            <h2 className="font-serif text-lg font-semibold text-chocolate-800">
              Filtrer par catégorie
            </h2>
          </div>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.key}
                onClick={() => setSelectedCategory(category.key)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 ${
                  selectedCategory === category.key
                    ? 'bg-rose-500 text-white shadow-md'
                    : 'bg-white text-chocolate-700 hover:bg-rose-100 border border-chocolate-200'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Grille des produits */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-300"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-300 rounded mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded mb-4"></div>
                  <div className="h-6 bg-gray-300 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredProducts.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-chocolate-600 text-lg">
              Aucun produit trouvé dans cette catégorie.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 fade-in"
              >
                <div className="relative h-48 bg-gradient-to-br from-cream-200 to-rose-100">
                  {product.image_url ? (
                    <Image
                      src={product.image_url}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <ShoppingCart className="h-16 w-16 text-chocolate-400" />
                    </div>
                  )}
                  <div className="absolute top-2 right-2 bg-rose-500 text-white px-2 py-1 rounded-full text-sm font-semibold">
                    {product.price.toFixed(2)}€
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-2">
                    {product.name}
                  </h3>
                  <p className="text-chocolate-600 text-sm mb-4 line-clamp-2">
                    {product.description}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-chocolate-800">
                      {product.price.toFixed(2)}€
                    </span>
                    <Link
                      href={`/product/${product.id}`}
                      className="bg-rose-500 hover:bg-rose-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      {t.products.order}
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
