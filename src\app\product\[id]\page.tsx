'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import Image from 'next/image'
import Link from 'next/link'
import { ArrowLeft, ShoppingCart, Star, Plus, Minus } from 'lucide-react'
import { translations } from '@/lib/translations'
import { supabase, type Product } from '@/lib/supabase'

export default function ProductDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [language] = useState<'fr' | 'en'>('fr')
  const [product, setProduct] = useState<Product | null>(null)
  const [quantity, setQuantity] = useState(1)
  const [loading, setLoading] = useState(true)
  const t = translations[language]

  useEffect(() => {
    if (params.id) {
      fetchProduct(params.id as string)
    }
  }, [params.id])

  const fetchProduct = async (id: string) => {
    setLoading(true)
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .eq('available', true)
      .single()

    if (data && !error) {
      setProduct(data)
    } else {
      router.push('/products')
    }
    setLoading(false)
  }

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change
    if (newQuantity >= 1 && newQuantity <= 10) {
      setQuantity(newQuantity)
    }
  }

  const handleOrder = () => {
    if (product) {
      const orderData = {
        productId: product.id,
        productName: product.name,
        quantity: quantity,
        unitPrice: product.price,
        totalPrice: product.price * quantity
      }
      
      // Stocker les données de commande dans localStorage pour la page de commande
      localStorage.setItem('orderData', JSON.stringify(orderData))
      router.push('/order')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-rose-500"></div>
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-chocolate-800 mb-4">Produit non trouvé</h1>
          <Link
            href="/products"
            className="text-rose-500 hover:text-rose-600 font-medium"
          >
            Retour aux produits
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Bouton retour */}
        <Link
          href="/products"
          className="inline-flex items-center text-chocolate-600 hover:text-chocolate-800 mb-8 transition-colors"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Retour aux produits
        </Link>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Image du produit */}
          <div className="relative">
            <div className="aspect-square rounded-lg overflow-hidden bg-gradient-to-br from-cream-200 to-rose-100 shadow-lg">
              {product.image_url ? (
                <Image
                  src={product.image_url}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <ShoppingCart className="h-32 w-32 text-chocolate-400" />
                </div>
              )}
            </div>
          </div>

          {/* Détails du produit */}
          <div className="flex flex-col justify-center">
            <div className="mb-4">
              <span className="inline-block bg-rose-100 text-rose-800 text-sm font-medium px-3 py-1 rounded-full mb-4">
                {t.products.filter[product.category as keyof typeof t.products.filter]}
              </span>
            </div>

            <h1 className="font-handwriting text-4xl md:text-5xl font-bold text-chocolate-800 mb-4">
              {product.name}
            </h1>

            <div className="flex items-center mb-6">
              <div className="flex items-center mr-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className="h-5 w-5 text-yellow-400 fill-current"
                  />
                ))}
              </div>
              <span className="text-chocolate-600 text-sm">(4.8/5 - 24 avis)</span>
            </div>

            <p className="text-chocolate-700 text-lg mb-8 leading-relaxed">
              {product.description}
            </p>

            <div className="mb-8">
              <span className="text-3xl font-bold text-chocolate-800">
                {product.price.toFixed(2)}€
              </span>
              <span className="text-chocolate-600 ml-2">par unité</span>
            </div>

            {/* Sélecteur de quantité */}
            <div className="mb-8">
              <label className="block text-chocolate-800 font-medium mb-3">
                Quantité
              </label>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleQuantityChange(-1)}
                  disabled={quantity <= 1}
                  className="w-10 h-10 rounded-full bg-chocolate-200 hover:bg-chocolate-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                >
                  <Minus className="h-4 w-4 text-chocolate-800" />
                </button>
                <span className="text-xl font-semibold text-chocolate-800 min-w-[2rem] text-center">
                  {quantity}
                </span>
                <button
                  onClick={() => handleQuantityChange(1)}
                  disabled={quantity >= 10}
                  className="w-10 h-10 rounded-full bg-chocolate-200 hover:bg-chocolate-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
                >
                  <Plus className="h-4 w-4 text-chocolate-800" />
                </button>
              </div>
            </div>

            {/* Total et bouton de commande */}
            <div className="border-t border-chocolate-200 pt-6">
              <div className="flex items-center justify-between mb-6">
                <span className="text-lg font-medium text-chocolate-800">Total:</span>
                <span className="text-2xl font-bold text-chocolate-800">
                  {(product.price * quantity).toFixed(2)}€
                </span>
              </div>

              <button
                onClick={handleOrder}
                className="w-full bg-rose-500 hover:bg-rose-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center space-x-2"
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Commander maintenant</span>
              </button>

              <p className="text-sm text-chocolate-600 mt-4 text-center">
                Livraison gratuite pour les commandes de plus de 30€
              </p>
            </div>
          </div>
        </div>

        {/* Informations supplémentaires */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <div className="bg-rose-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <ShoppingCart className="h-8 w-8 text-rose-600" />
            </div>
            <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-2">
              Fait Maison
            </h3>
            <p className="text-chocolate-600 text-sm">
              Préparé artisanalement avec des ingrédients frais et de qualité
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <div className="bg-cream-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <Star className="h-8 w-8 text-chocolate-600" />
            </div>
            <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-2">
              Qualité Premium
            </h3>
            <p className="text-chocolate-600 text-sm">
              Sélection rigoureuse des meilleurs ingrédients pour un goût authentique
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md text-center">
            <div className="bg-chocolate-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
              <ArrowLeft className="h-8 w-8 text-chocolate-700" />
            </div>
            <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-2">
              Livraison Rapide
            </h3>
            <p className="text-chocolate-600 text-sm">
              Commande préparée et livrée dans les meilleurs délais
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
