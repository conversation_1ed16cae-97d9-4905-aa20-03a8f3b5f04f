from django.core.management.base import BaseCommand
from bakery.models import Category


class Command(BaseCommand):
    help = 'Crée les catégories par défaut'

    def handle(self, *args, **options):
        self.stdout.write('🏷️ Création des catégories par défaut...')
        
        # Catégories par défaut
        default_categories = [
            {
                'name': 'gateaux',
                'display_name': 'Gâteaux',
                'description': 'Gâteaux artisanaux, tartes et desserts'
            },
            {
                'name': 'pain',
                'display_name': 'Pains',
                'description': 'Pains frais et spécialités boulangères'
            },
            {
                'name': 'sables',
                'display_name': 'Sablés',
                'description': 'Biscuits sablés et cookies maison'
            },
            {
                'name': 'viennoiseries',
                'display_name': 'Viennoiseries',
                'description': 'Croissants, pains au chocolat et autres viennoiseries'
            },
            {
                'name': 'autres',
                'display_name': 'Autres',
                'description': 'Autres spécialités de la boulangerie'
            }
        ]
        
        for cat_data in default_categories:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'display_name': cat_data['display_name'],
                    'description': cat_data['description'],
                    'active': True
                }
            )
            if created:
                self.stdout.write(f'✅ Catégorie créée: {category.display_name}')
            else:
                self.stdout.write(f'⏭️  Catégorie existante: {category.display_name}')
        
        self.stdout.write(
            self.style.SUCCESS('🎉 Catégories par défaut créées avec succès !')
        )
