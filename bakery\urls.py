from django.urls import path
from . import views

app_name = 'bakery'

urlpatterns = [
    # Pages principales
    path('', views.home, name='home'),
    path('products/', views.products, name='products'),
    path('product/<int:product_id>/', views.product_detail, name='product_detail'),
    path('order/', views.order, name='order'),
    path('order/success/', views.order_success, name='order_success'),
    path('contact/', views.contact, name='contact'),
    path('contact/success/', views.contact_success, name='contact_success'),
    
    # Ajouter au panier
    path('add-to-cart/<int:product_id>/', views.add_to_cart, name='add_to_cart'),

    # API pour les filtres
    path('api/products/', views.api_products, name='api_products'),
]
