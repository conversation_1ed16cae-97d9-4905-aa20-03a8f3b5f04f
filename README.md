# RBA Kerry - Site Web Boulangerie Artisanale

Un site web moderne et dynamique pour une boulangerie artisanale, développé avec Next.js 14, TypeScript, TailwindCSS et Supabase.

## 🌟 Fonctionnalités

### Frontend
- ✅ **Page d'accueil** avec carrousel d'images et design artisanal
- ✅ **Catalogue de produits** avec filtres dynamiques par catégorie
- ✅ **Pages produits détaillées** avec système de commande
- ✅ **Formulaire de commande** avec calendrier de livraison
- ✅ **Section avis clients** depuis la base de données
- ✅ **Page de contact** avec formulaire
- ✅ **Design responsive** (mobile + desktop)
- ✅ **Thème gourmand** (couleurs crème, chocolat, rose pastel)
- ✅ **Support multilingue** (Français/Anglais)

### Backend & Admin
- ✅ **Panel d'administration** sécurisé
- ✅ **Gestion des produits** (CRUD complet)
- ✅ **Gestion des commandes** avec suivi de statut
- ✅ **Gestion des avis clients** avec modération
- ✅ **Gestion des messages de contact**
- ✅ **Tableau de bord** avec statistiques

### Base de données
- ✅ **Supabase PostgreSQL** avec schéma optimisé
- ✅ **Tables**: products, orders, reviews, contacts
- ✅ **Sécurité RLS** (Row Level Security)
- ✅ **Données d'exemple** incluses

## 🚀 Installation et Configuration

### Prérequis
- Node.js 18+ et npm/yarn
- Compte Supabase (gratuit)

### 1. Cloner le projet
```bash
git clone <repository-url>
cd rba_kerry
```

### 2. Installer les dépendances
```bash
npm install
# ou
yarn install
```

### 3. Configuration Supabase

#### Créer un projet Supabase
1. Allez sur [supabase.com](https://supabase.com)
2. Créez un nouveau projet
3. Notez l'URL du projet et la clé API publique

#### Configurer la base de données
1. Dans le dashboard Supabase, allez dans "SQL Editor"
2. Copiez et exécutez le contenu de `supabase/migrations/001_initial_schema.sql`

### 4. Variables d'environnement
```bash
# Copiez le fichier d'exemple
cp .env.local.example .env.local

# Éditez .env.local avec vos clés Supabase
NEXT_PUBLIC_SUPABASE_URL=votre_url_supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre_cle_publique_supabase
```

### 5. Lancer le projet
```bash
npm run dev
# ou
yarn dev
```

Le site sera accessible sur [http://localhost:3000](http://localhost:3000)

## 📁 Structure du Projet

```
rba_kerry/
├── src/
│   ├── app/                    # App Router Next.js 14
│   │   ├── page.tsx           # Page d'accueil
│   │   ├── products/          # Catalogue produits
│   │   ├── product/[id]/      # Page produit détaillée
│   │   ├── order/             # Formulaire de commande
│   │   ├── contact/           # Page contact
│   │   ├── admin/             # Panel admin
│   │   └── api/               # API Routes (si nécessaire)
│   ├── components/            # Composants réutilisables
│   │   ├── Navbar.tsx
│   │   └── Footer.tsx
│   ├── lib/                   # Utilitaires et config
│   │   ├── supabase.ts        # Configuration Supabase
│   │   └── translations.ts    # Traductions FR/EN
│   └── styles/
├── public/                    # Images et assets statiques
├── supabase/                  # Migrations et config DB
└── Configuration files
```

## 🎨 Personnalisation

### Couleurs et Design
Les couleurs sont définies dans `tailwind.config.ts`:
- **Crème**: Tons chauds et accueillants
- **Chocolat**: Couleurs principales
- **Rose**: Accents et boutons

### Polices
- **Handwriting**: Dancing Script (titres)
- **Serif**: Playfair Display (sous-titres)
- **Sans**: Inter (texte)

### Images
Ajoutez vos images dans le dossier `public/images/` et mettez à jour les références dans les composants.

## 🚀 Déploiement

### Vercel (Recommandé)
1. Connectez votre repository GitHub à Vercel
2. Ajoutez les variables d'environnement dans les settings Vercel
3. Déployez automatiquement

### Autres plateformes
Le projet est compatible avec:
- Netlify
- Railway
- Render
- Heroku

## 📊 Administration

### Accès Admin
- URL: `/admin`
- **Note**: Actuellement ouvert (à sécuriser en production)

### Fonctionnalités Admin
- **Dashboard**: Vue d'ensemble des statistiques
- **Produits**: Ajouter/modifier/supprimer des produits
- **Commandes**: Suivre et gérer les commandes
- **Messages**: Répondre aux contacts clients
- **Avis**: Modérer les avis clients

## 🔒 Sécurité

### À implémenter en production:
1. **Authentification admin** avec Supabase Auth
2. **Restrictions RLS** plus strictes
3. **Validation côté serveur** renforcée
4. **Rate limiting** sur les API
5. **HTTPS** obligatoire

## 🌍 Multilingue

Le site supporte le français et l'anglais:
- Fichier de traductions: `src/lib/translations.ts`
- Changement de langue via la navbar
- Extensible à d'autres langues

## 📱 Responsive Design

- **Mobile First** avec TailwindCSS
- **Breakpoints**: sm, md, lg, xl
- **Navigation mobile** avec menu hamburger
- **Images optimisées** avec Next.js Image

## 🛠️ Technologies Utilisées

- **Frontend**: Next.js 14, TypeScript, TailwindCSS
- **Backend**: Supabase (PostgreSQL + API)
- **UI**: Lucide React (icônes)
- **Forms**: React Hook Form
- **Dates**: date-fns
- **Déploiement**: Vercel

## 📞 Support

Pour toute question ou personnalisation:
1. Consultez la documentation des technologies utilisées
2. Vérifiez les issues GitHub
3. Contactez l'équipe de développement

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**Développé avec ❤️ pour RBA Kerry Boulangerie Artisanale**
