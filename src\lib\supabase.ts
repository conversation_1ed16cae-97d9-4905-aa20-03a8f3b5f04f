import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types pour la base de données
export interface Product {
  id: string
  name: string
  description: string
  price: number
  image_url: string
  category: 'gateaux' | 'pain' | 'sables' | 'viennoiseries' | 'autres'
  available: boolean
  created_at: string
  updated_at: string
}

export interface Order {
  id: string
  customer_name: string
  customer_email: string
  customer_phone: string
  customer_address: string
  delivery_date: string
  products: OrderProduct[]
  total_amount: number
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'delivered' | 'cancelled'
  notes?: string
  created_at: string
  updated_at: string
}

export interface OrderProduct {
  product_id: string
  product_name: string
  quantity: number
  unit_price: number
}

export interface Review {
  id: string
  customer_name: string
  rating: number
  comment: string
  approved: boolean
  created_at: string
}

export interface Contact {
  id: string
  name: string
  email: string
  subject: string
  message: string
  created_at: string
}
