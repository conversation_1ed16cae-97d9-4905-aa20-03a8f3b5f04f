from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone


class Category(models.Model):
    """Catégories de produits"""
    name = models.CharField(max_length=50, unique=True, verbose_name='Nom technique')
    display_name = models.CharField(max_length=100, verbose_name='Nom affiché')
    description = models.TextField(blank=True, verbose_name='Description')
    active = models.BooleanField(default=True, verbose_name='Actif')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Catégorie'
        verbose_name_plural = 'Catégories'
        ordering = ['display_name']

    def __str__(self):
        return self.display_name


class Product(models.Model):
    """Produits de la boulangerie"""
    name = models.CharField(max_length=200, verbose_name='Nom')
    description = models.TextField(verbose_name='Description')
    price = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        validators=[MinValueValidator(0)],
        verbose_name='Prix (€)'
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.CASCADE,
        verbose_name='Catégorie'
    )
    image = models.ImageField(
        upload_to='products/', 
        blank=True, 
        null=True,
        verbose_name='Image'
    )
    available = models.BooleanField(default=True, verbose_name='Disponible')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Produit'
        verbose_name_plural = 'Produits'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.name


class Order(models.Model):
    """Commandes clients"""
    STATUS_CHOICES = [
        ('pending', 'En attente'),
        ('confirmed', 'Confirmée'),
        ('preparing', 'En préparation'),
        ('ready', 'Prête'),
        ('delivered', 'Livrée'),
        ('cancelled', 'Annulée'),
    ]
    
    # Informations client
    customer_name = models.CharField(max_length=200, verbose_name='Nom du client')
    customer_email = models.EmailField(verbose_name='Email')
    customer_phone = models.CharField(max_length=20, verbose_name='Téléphone')
    customer_address = models.TextField(verbose_name='Adresse de livraison')
    
    # Détails commande
    delivery_date = models.DateField(verbose_name='Date de livraison souhaitée')
    total_amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='Montant total (€)'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='Statut'
    )
    notes = models.TextField(blank=True, verbose_name='Notes spéciales')
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Commande'
        verbose_name_plural = 'Commandes'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Commande #{self.id} - {self.customer_name}"


class OrderItem(models.Model):
    """Articles d'une commande"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    
    class Meta:
        verbose_name = 'Article de commande'
        verbose_name_plural = 'Articles de commande'
    
    def __str__(self):
        return f"{self.product.name} x{self.quantity}"
    
    @property
    def total_price(self):
        return self.quantity * self.unit_price


class Review(models.Model):
    """Avis clients"""
    customer_name = models.CharField(max_length=200, verbose_name='Nom du client')
    rating = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        verbose_name='Note (1-5)'
    )
    comment = models.TextField(verbose_name='Commentaire')
    approved = models.BooleanField(default=False, verbose_name='Approuvé')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Avis client'
        verbose_name_plural = 'Avis clients'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Avis de {self.customer_name} - {self.rating}/5"


class Contact(models.Model):
    """Messages de contact"""
    name = models.CharField(max_length=200, verbose_name='Nom')
    email = models.EmailField(verbose_name='Email')
    subject = models.CharField(max_length=200, verbose_name='Sujet')
    message = models.TextField(verbose_name='Message')
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        verbose_name = 'Message de contact'
        verbose_name_plural = 'Messages de contact'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"Message de {self.name} - {self.subject}"
