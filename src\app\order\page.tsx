'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { Calendar, Clock, MapPin, Phone, Mail, User, MessageSquare } from 'lucide-react'
import { translations } from '@/lib/translations'
import { supabase, type Order, type OrderProduct } from '@/lib/supabase'

interface OrderFormData {
  customerName: string
  customerEmail: string
  customerPhone: string
  customerAddress: string
  deliveryDate: string
  notes?: string
}

interface OrderData {
  productId: string
  productName: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

export default function OrderPage() {
  const router = useRouter()
  const [language] = useState<'fr' | 'en'>('fr')
  const [orderData, setOrderData] = useState<OrderData | null>(null)
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const t = translations[language]

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm<OrderFormData>()

  useEffect(() => {
    // Récupérer les données de commande depuis localStorage
    const storedOrderData = localStorage.getItem('orderData')
    if (storedOrderData) {
      setOrderData(JSON.parse(storedOrderData))
    } else {
      router.push('/products')
    }
  }, [router])

  const onSubmit = async (data: OrderFormData) => {
    if (!orderData) return

    setLoading(true)
    try {
      const orderProducts: OrderProduct[] = [{
        product_id: orderData.productId,
        product_name: orderData.productName,
        quantity: orderData.quantity,
        unit_price: orderData.unitPrice
      }]

      const order: Omit<Order, 'id' | 'created_at' | 'updated_at'> = {
        customer_name: data.customerName,
        customer_email: data.customerEmail,
        customer_phone: data.customerPhone,
        customer_address: data.customerAddress,
        delivery_date: data.deliveryDate,
        products: orderProducts,
        total_amount: orderData.totalPrice,
        status: 'pending',
        notes: data.notes
      }

      const { error } = await supabase
        .from('orders')
        .insert([order])

      if (error) {
        throw error
      }

      setSuccess(true)
      localStorage.removeItem('orderData')
      
      // Redirection après 3 secondes
      setTimeout(() => {
        router.push('/products')
      }, 3000)

    } catch (error) {
      console.error('Erreur lors de la commande:', error)
      alert('Une erreur est survenue lors de la commande. Veuillez réessayer.')
    } finally {
      setLoading(false)
    }
  }

  // Générer les dates disponibles (à partir de demain, excluant les dimanches)
  const getAvailableDates = () => {
    const dates = []
    const today = new Date()
    
    for (let i = 1; i <= 14; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      
      // Exclure les dimanches (0 = dimanche)
      if (date.getDay() !== 0) {
        dates.push({
          value: date.toISOString().split('T')[0],
          label: date.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
        })
      }
    }
    
    return dates
  }

  if (success) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4 text-center">
          <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6">
            <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="font-serif text-2xl font-bold text-chocolate-800 mb-4">
            Commande confirmée !
          </h2>
          <p className="text-chocolate-600 mb-6">
            Votre commande a été enregistrée avec succès. Nous vous contacterons bientôt pour confirmer les détails.
          </p>
          <p className="text-sm text-chocolate-500">
            Redirection automatique vers les produits...
          </p>
        </div>
      </div>
    )
  }

  if (!orderData) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-rose-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="font-handwriting text-4xl md:text-5xl font-bold text-chocolate-800 mb-4">
            {t.order.title}
          </h1>
          <p className="text-chocolate-600 text-lg">
            Complétez vos informations pour finaliser votre commande
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Résumé de commande */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
              <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                Résumé de commande
              </h3>
              
              <div className="border-b border-chocolate-200 pb-4 mb-4">
                <div className="flex justify-between items-start mb-2">
                  <span className="font-medium text-chocolate-800">{orderData.productName}</span>
                </div>
                <div className="flex justify-between text-sm text-chocolate-600">
                  <span>Quantité: {orderData.quantity}</span>
                  <span>{orderData.unitPrice.toFixed(2)}€ / unité</span>
                </div>
              </div>
              
              <div className="flex justify-between items-center text-lg font-bold text-chocolate-800">
                <span>Total:</span>
                <span>{orderData.totalPrice.toFixed(2)}€</span>
              </div>
              
              <div className="mt-4 p-3 bg-rose-50 rounded-lg">
                <p className="text-sm text-rose-800">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Livraison gratuite pour les commandes de plus de 30€
                </p>
              </div>
            </div>
          </div>

          {/* Formulaire de commande */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Informations personnelles */}
                <div>
                  <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Informations personnelles
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-chocolate-700 mb-2">
                        {t.order.form.name} *
                      </label>
                      <input
                        type="text"
                        {...register('customerName', { required: 'Le nom est requis' })}
                        className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                        placeholder="Votre nom complet"
                      />
                      {errors.customerName && (
                        <p className="text-red-500 text-sm mt-1">{errors.customerName.message}</p>
                      )}
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-chocolate-700 mb-2">
                        {t.order.form.phone} *
                      </label>
                      <input
                        type="tel"
                        {...register('customerPhone', { required: 'Le téléphone est requis' })}
                        className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                        placeholder="06 12 34 56 78"
                      />
                      {errors.customerPhone && (
                        <p className="text-red-500 text-sm mt-1">{errors.customerPhone.message}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-chocolate-700 mb-2">
                      {t.order.form.email} *
                    </label>
                    <input
                      type="email"
                      {...register('customerEmail', { 
                        required: 'L\'email est requis',
                        pattern: {
                          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                          message: 'Email invalide'
                        }
                      })}
                      className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                    {errors.customerEmail && (
                      <p className="text-red-500 text-sm mt-1">{errors.customerEmail.message}</p>
                    )}
                  </div>
                </div>

                {/* Adresse de livraison */}
                <div>
                  <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Adresse de livraison
                  </h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-chocolate-700 mb-2">
                      {t.order.form.address} *
                    </label>
                    <textarea
                      {...register('customerAddress', { required: 'L\'adresse est requise' })}
                      rows={3}
                      className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                      placeholder="Votre adresse complète"
                    />
                    {errors.customerAddress && (
                      <p className="text-red-500 text-sm mt-1">{errors.customerAddress.message}</p>
                    )}
                  </div>
                </div>

                {/* Date de livraison */}
                <div>
                  <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Date de livraison souhaitée
                  </h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-chocolate-700 mb-2">
                      {t.order.form.date} *
                    </label>
                    <select
                      {...register('deliveryDate', { required: 'La date de livraison est requise' })}
                      className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                    >
                      <option value="">Sélectionnez une date</option>
                      {getAvailableDates().map((date) => (
                        <option key={date.value} value={date.value}>
                          {date.label}
                        </option>
                      ))}
                    </select>
                    {errors.deliveryDate && (
                      <p className="text-red-500 text-sm mt-1">{errors.deliveryDate.message}</p>
                    )}
                  </div>
                </div>

                {/* Notes spéciales */}
                <div>
                  <h3 className="font-serif text-lg font-semibold text-chocolate-800 mb-4 flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2" />
                    Notes spéciales (optionnel)
                  </h3>
                  
                  <div>
                    <label className="block text-sm font-medium text-chocolate-700 mb-2">
                      {t.order.form.notes}
                    </label>
                    <textarea
                      {...register('notes')}
                      rows={3}
                      className="w-full px-3 py-2 border border-chocolate-300 rounded-lg focus:ring-2 focus:ring-rose-500 focus:border-transparent"
                      placeholder="Instructions spéciales, allergies, préférences..."
                    />
                  </div>
                </div>

                {/* Bouton de soumission */}
                <div className="pt-6">
                  <button
                    type="submit"
                    disabled={loading}
                    className="w-full bg-rose-500 hover:bg-rose-600 disabled:bg-rose-300 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg disabled:transform-none"
                  >
                    {loading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Traitement en cours...
                      </div>
                    ) : (
                      t.order.form.submit
                    )}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
