'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronLeft, ChevronRight, Star, Award, Clock, Heart } from 'lucide-react'
import { translations } from '@/lib/translations'
import { supabase, type Review } from '@/lib/supabase'

export default function HomePage() {
  const [language] = useState<'fr' | 'en'>('fr')
  const [currentSlide, setCurrentSlide] = useState(0)
  const [reviews, setReviews] = useState<Review[]>([])
  const t = translations[language]

  // Images du carrousel (vous pouvez remplacer par de vraies images)
  const carouselImages = [
    {
      src: '/images/hero-1.jpg',
      alt: 'Gâteaux artisanaux',
      title: 'Gâteaux Artisanaux',
      description: 'Des créations uniques pour vos moments spéciaux'
    },
    {
      src: '/images/hero-2.jpg',
      alt: 'Pains frais',
      title: 'Pains Frais',
      description: 'Cuits chaque matin avec des ingrédients de qualité'
    },
    {
      src: '/images/hero-3.jpg',
      alt: 'Sablés maison',
      title: 'Sab<PERSON><PERSON>',
      description: 'Croustillants et fondants, un délice à chaque bouchée'
    }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselImages.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [carouselImages.length])

  useEffect(() => {
    fetchReviews()
  }, [])

  const fetchReviews = async () => {
    const { data, error } = await supabase
      .from('reviews')
      .select('*')
      .eq('approved', true)
      .order('created_at', { ascending: false })
      .limit(3)

    if (data && !error) {
      setReviews(data)
    }
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselImages.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + carouselImages.length) % carouselImages.length)
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section avec Carrousel */}
      <section className="relative h-[70vh] overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-chocolate-900/70 to-chocolate-600/50 z-10"></div>
        
        {/* Carrousel d'images */}
        <div className="relative h-full">
          {carouselImages.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ${
                index === currentSlide ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <div className="w-full h-full bg-gradient-to-br from-cream-200 via-rose-100 to-chocolate-200"></div>
            </div>
          ))}
        </div>

        {/* Contenu Hero */}
        <div className="absolute inset-0 z-20 flex items-center justify-center">
          <div className="text-center text-white px-4 max-w-4xl">
            <h1 className="font-handwriting text-6xl md:text-8xl font-bold mb-4 text-cream-50 drop-shadow-lg">
              {t.home.hero.title}
            </h1>
            <p className="font-serif text-xl md:text-2xl mb-2 text-cream-100">
              {t.home.hero.subtitle}
            </p>
            <p className="text-lg md:text-xl mb-8 text-cream-200 max-w-2xl mx-auto">
              {t.home.hero.description}
            </p>
            <Link
              href="/products"
              className="inline-block bg-rose-500 hover:bg-rose-600 text-white font-semibold py-4 px-8 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              {t.home.hero.cta}
            </Link>
          </div>
        </div>

        {/* Contrôles du carrousel */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-30 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
        >
          <ChevronLeft className="h-6 w-6 text-white" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-30 bg-white/20 hover:bg-white/30 rounded-full p-2 transition-colors"
        >
          <ChevronRight className="h-6 w-6 text-white" />
        </button>

        {/* Indicateurs */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-30 flex space-x-2">
          {carouselImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Section Caractéristiques */}
      <section className="py-16 bg-cream-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="font-serif text-4xl font-bold text-center text-chocolate-800 mb-12">
            {t.home.features.title}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center fade-in">
              <div className="bg-rose-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Award className="h-10 w-10 text-rose-600" />
              </div>
              <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                {t.home.features.quality.title}
              </h3>
              <p className="text-chocolate-600">
                {t.home.features.quality.description}
              </p>
            </div>
            <div className="text-center fade-in">
              <div className="bg-cream-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Clock className="h-10 w-10 text-chocolate-600" />
              </div>
              <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                {t.home.features.fresh.title}
              </h3>
              <p className="text-chocolate-600">
                {t.home.features.fresh.description}
              </p>
            </div>
            <div className="text-center fade-in">
              <div className="bg-chocolate-200 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                <Heart className="h-10 w-10 text-chocolate-700" />
              </div>
              <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                {t.home.features.custom.title}
              </h3>
              <p className="text-chocolate-600">
                {t.home.features.custom.description}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Section Avis Clients */}
      {reviews.length > 0 && (
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="font-serif text-4xl font-bold text-center text-chocolate-800 mb-12">
              {t.reviews.title}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {reviews.map((review) => (
                <div key={review.id} className="bg-cream-50 rounded-lg p-6 shadow-md">
                  <div className="flex items-center mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-5 w-5 ${
                          i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <p className="text-chocolate-700 mb-4 italic">"{review.comment}"</p>
                  <p className="font-semibold text-chocolate-800">- {review.customer_name}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  )
}
