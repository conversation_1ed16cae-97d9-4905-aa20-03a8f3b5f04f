'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { 
  Package, 
  ShoppingBag, 
  MessageSquare, 
  Star, 
  TrendingUp, 
  Users,
  Calendar,
  Euro
} from 'lucide-react'
import { supabase } from '@/lib/supabase'

interface DashboardStats {
  totalProducts: number
  totalOrders: number
  pendingOrders: number
  totalRevenue: number
  totalContacts: number
  totalReviews: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    pendingOrders: 0,
    totalRevenue: 0,
    totalContacts: 0,
    totalReviews: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardStats()
  }, [])

  const fetchDashboardStats = async () => {
    setLoading(true)
    try {
      // Récupérer les statistiques en parallèle
      const [
        productsResult,
        ordersResult,
        contactsResult,
        reviewsResult
      ] = await Promise.all([
        supabase.from('products').select('id', { count: 'exact' }),
        supabase.from('orders').select('total_amount, status', { count: 'exact' }),
        supabase.from('contacts').select('id', { count: 'exact' }),
        supabase.from('reviews').select('id', { count: 'exact' })
      ])

      const totalProducts = productsResult.count || 0
      const totalOrders = ordersResult.count || 0
      const totalContacts = contactsResult.count || 0
      const totalReviews = reviewsResult.count || 0

      // Calculer le chiffre d'affaires et les commandes en attente
      const orders = ordersResult.data || []
      const totalRevenue = orders.reduce((sum, order) => sum + (order.total_amount || 0), 0)
      const pendingOrders = orders.filter(order => order.status === 'pending').length

      setStats({
        totalProducts,
        totalOrders,
        pendingOrders,
        totalRevenue,
        totalContacts,
        totalReviews
      })
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error)
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color, 
    href 
  }: { 
    title: string
    value: string | number
    icon: any
    color: string
    href?: string
  }) => {
    const content = (
      <div className={`bg-white rounded-lg shadow-md p-6 ${href ? 'hover:shadow-lg transition-shadow cursor-pointer' : ''}`}>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value}</p>
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            <Icon className="h-6 w-6 text-white" />
          </div>
        </div>
      </div>
    )

    return href ? <Link href={href}>{content}</Link> : content
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-rose-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="font-handwriting text-4xl font-bold text-chocolate-800">
            Tableau de Bord Admin
          </h1>
          <p className="text-chocolate-600 mt-2">
            Gérez votre boulangerie en un coup d'œil
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Statistiques */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Produits"
            value={stats.totalProducts}
            icon={Package}
            color="bg-blue-500"
            href="/admin/products"
          />
          <StatCard
            title="Commandes"
            value={stats.totalOrders}
            icon={ShoppingBag}
            color="bg-green-500"
            href="/admin/orders"
          />
          <StatCard
            title="En attente"
            value={stats.pendingOrders}
            icon={Calendar}
            color="bg-yellow-500"
            href="/admin/orders"
          />
          <StatCard
            title="Chiffre d'affaires"
            value={`${stats.totalRevenue.toFixed(2)}€`}
            icon={Euro}
            color="bg-purple-500"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            title="Messages"
            value={stats.totalContacts}
            icon={MessageSquare}
            color="bg-indigo-500"
            href="/admin/contacts"
          />
          <StatCard
            title="Avis clients"
            value={stats.totalReviews}
            icon={Star}
            color="bg-orange-500"
            href="/admin/reviews"
          />
          <StatCard
            title="Clients"
            value={stats.totalOrders}
            icon={Users}
            color="bg-pink-500"
          />
          <StatCard
            title="Croissance"
            value="+12%"
            icon={TrendingUp}
            color="bg-teal-500"
          />
        </div>

        {/* Actions rapides */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Link
            href="/admin/products"
            className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-blue-100 rounded-full p-3">
                <Package className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h3 className="font-serif text-lg font-semibold text-chocolate-800">
                  Gérer les Produits
                </h3>
                <p className="text-chocolate-600 text-sm">
                  Ajouter, modifier ou supprimer des produits
                </p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/orders"
            className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 rounded-full p-3">
                <ShoppingBag className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="font-serif text-lg font-semibold text-chocolate-800">
                  Gérer les Commandes
                </h3>
                <p className="text-chocolate-600 text-sm">
                  Voir et traiter les commandes clients
                </p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/contacts"
            className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-indigo-100 rounded-full p-3">
                <MessageSquare className="h-8 w-8 text-indigo-600" />
              </div>
              <div>
                <h3 className="font-serif text-lg font-semibold text-chocolate-800">
                  Messages Clients
                </h3>
                <p className="text-chocolate-600 text-sm">
                  Répondre aux messages de contact
                </p>
              </div>
            </div>
          </Link>

          <Link
            href="/admin/reviews"
            className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow"
          >
            <div className="flex items-center space-x-4">
              <div className="bg-orange-100 rounded-full p-3">
                <Star className="h-8 w-8 text-orange-600" />
              </div>
              <div>
                <h3 className="font-serif text-lg font-semibold text-chocolate-800">
                  Avis Clients
                </h3>
                <p className="text-chocolate-600 text-sm">
                  Modérer et gérer les avis clients
                </p>
              </div>
            </div>
          </Link>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-purple-100 rounded-full p-3">
                <TrendingUp className="h-8 w-8 text-purple-600" />
              </div>
              <div>
                <h3 className="font-serif text-lg font-semibold text-chocolate-800">
                  Statistiques
                </h3>
                <p className="text-chocolate-600 text-sm">
                  Analyser les performances
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center space-x-4">
              <div className="bg-teal-100 rounded-full p-3">
                <Users className="h-8 w-8 text-teal-600" />
              </div>
              <div>
                <h3 className="font-serif text-lg font-semibold text-chocolate-800">
                  Paramètres
                </h3>
                <p className="text-chocolate-600 text-sm">
                  Configuration du site
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
