'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { fr } from 'date-fns/locale'
import { Star, Check, X, Trash2, Eye } from 'lucide-react'
import { supabase, type Review } from '@/lib/supabase'

export default function AdminReviewsPage() {
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'approved' | 'pending'>('all')

  useEffect(() => {
    fetchReviews()
  }, [])

  const fetchReviews = async () => {
    setLoading(true)
    const { data, error } = await supabase
      .from('reviews')
      .select('*')
      .order('created_at', { ascending: false })

    if (data && !error) {
      setReviews(data)
    }
    setLoading(false)
  }

  const updateReviewStatus = async (reviewId: string, approved: boolean) => {
    const { error } = await supabase
      .from('reviews')
      .update({ approved })
      .eq('id', reviewId)

    if (!error) {
      setReviews(reviews.map(review => 
        review.id === reviewId 
          ? { ...review, approved }
          : review
      ))
    }
  }

  const deleteReview = async (reviewId: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer cet avis ?')) {
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('id', reviewId)

      if (!error) {
        setReviews(reviews.filter(review => review.id !== reviewId))
      }
    }
  }

  const renderStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  const filteredReviews = reviews.filter(review => {
    if (filter === 'approved') return review.approved
    if (filter === 'pending') return !review.approved
    return true
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-rose-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="font-handwriting text-4xl font-bold text-chocolate-800">
            Gestion des Avis Clients
          </h1>
          <p className="text-chocolate-600 mt-2">
            Modérez et gérez les avis laissés par vos clients
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filtres */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilter('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                filter === 'all'
                  ? 'bg-rose-500 text-white'
                  : 'bg-white text-chocolate-700 hover:bg-rose-100 border border-chocolate-200'
              }`}
            >
              Tous ({reviews.length})
            </button>
            <button
              onClick={() => setFilter('approved')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                filter === 'approved'
                  ? 'bg-rose-500 text-white'
                  : 'bg-white text-chocolate-700 hover:bg-rose-100 border border-chocolate-200'
              }`}
            >
              Approuvés ({reviews.filter(r => r.approved).length})
            </button>
            <button
              onClick={() => setFilter('pending')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                filter === 'pending'
                  ? 'bg-rose-500 text-white'
                  : 'bg-white text-chocolate-700 hover:bg-rose-100 border border-chocolate-200'
              }`}
            >
              En attente ({reviews.filter(r => !r.approved).length})
            </button>
          </div>
        </div>

        {filteredReviews.length === 0 ? (
          <div className="text-center py-12">
            <Star className="h-16 w-16 text-chocolate-400 mx-auto mb-4" />
            <p className="text-chocolate-600 text-lg">
              {filter === 'all' 
                ? 'Aucun avis trouvé.'
                : filter === 'approved'
                ? 'Aucun avis approuvé.'
                : 'Aucun avis en attente de modération.'
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredReviews.map((review) => (
              <div
                key={review.id}
                className={`bg-white rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${
                  review.approved ? 'border-l-4 border-green-500' : 'border-l-4 border-yellow-500'
                }`}
              >
                {/* Header avec nom et note */}
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="font-semibold text-chocolate-800">
                      {review.customer_name}
                    </h3>
                    <div className="flex items-center mt-1">
                      {renderStars(review.rating)}
                      <span className="ml-2 text-sm text-chocolate-600">
                        ({review.rating}/5)
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      review.approved 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {review.approved ? 'Approuvé' : 'En attente'}
                    </span>
                    <p className="text-xs text-chocolate-500 mt-1">
                      {format(new Date(review.created_at), 'dd MMM yyyy', { locale: fr })}
                    </p>
                  </div>
                </div>

                {/* Commentaire */}
                <div className="mb-4">
                  <p className="text-chocolate-700 text-sm italic">
                    "{review.comment}"
                  </p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-chocolate-100">
                  <div className="flex items-center space-x-2">
                    {!review.approved ? (
                      <button
                        onClick={() => updateReviewStatus(review.id, true)}
                        className="inline-flex items-center px-3 py-1 rounded text-xs font-medium bg-green-100 text-green-700 hover:bg-green-200 transition-colors"
                        title="Approuver"
                      >
                        <Check className="h-3 w-3 mr-1" />
                        Approuver
                      </button>
                    ) : (
                      <button
                        onClick={() => updateReviewStatus(review.id, false)}
                        className="inline-flex items-center px-3 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-700 hover:bg-yellow-200 transition-colors"
                        title="Retirer l'approbation"
                      >
                        <X className="h-3 w-3 mr-1" />
                        Masquer
                      </button>
                    )}
                  </div>
                  
                  <button
                    onClick={() => deleteReview(review.id)}
                    className="inline-flex items-center px-3 py-1 rounded text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200 transition-colors"
                    title="Supprimer"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    Supprimer
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Statistiques */}
        {reviews.length > 0 && (
          <div className="mt-12 bg-white rounded-lg shadow-md p-6">
            <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
              Statistiques des avis
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-chocolate-800">
                  {reviews.length}
                </div>
                <div className="text-sm text-chocolate-600">Total des avis</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {reviews.filter(r => r.approved).length}
                </div>
                <div className="text-sm text-chocolate-600">Avis approuvés</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {reviews.filter(r => !r.approved).length}
                </div>
                <div className="text-sm text-chocolate-600">En attente</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-chocolate-800">
                  {reviews.length > 0 
                    ? (reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1)
                    : '0'
                  }
                </div>
                <div className="text-sm text-chocolate-600">Note moyenne</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
