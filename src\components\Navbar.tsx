'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Menu, X, ShoppingBag, Globe } from 'lucide-react'
import { translations } from '@/lib/translations'

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const [language, setLanguage] = useState<'fr' | 'en'>('fr')
  
  const t = translations[language]

  const toggleLanguage = () => {
    setLanguage(language === 'fr' ? 'en' : 'fr')
  }

  return (
    <nav className="bg-cream-50 shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <ShoppingBag className="h-8 w-8 text-chocolate-600" />
              <span className="font-handwriting text-2xl font-bold text-chocolate-800">
                RBA Kerry
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link 
              href="/" 
              className="text-chocolate-700 hover:text-chocolate-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {t.nav.home}
            </Link>
            <Link 
              href="/products" 
              className="text-chocolate-700 hover:text-chocolate-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {t.nav.products}
            </Link>
            <Link 
              href="/contact" 
              className="text-chocolate-700 hover:text-chocolate-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {t.nav.contact}
            </Link>
            <Link 
              href="/admin" 
              className="text-chocolate-700 hover:text-chocolate-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              {t.nav.admin}
            </Link>
            
            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className="flex items-center space-x-1 text-chocolate-700 hover:text-chocolate-900 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              <Globe className="h-4 w-4" />
              <span>{language.toUpperCase()}</span>
            </button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={toggleLanguage}
              className="text-chocolate-700 hover:text-chocolate-900 p-2"
            >
              <Globe className="h-5 w-5" />
            </button>
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="text-chocolate-700 hover:text-chocolate-900 p-2"
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-cream-100 rounded-lg mt-2">
              <Link 
                href="/" 
                className="text-chocolate-700 hover:text-chocolate-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t.nav.home}
              </Link>
              <Link 
                href="/products" 
                className="text-chocolate-700 hover:text-chocolate-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t.nav.products}
              </Link>
              <Link 
                href="/contact" 
                className="text-chocolate-700 hover:text-chocolate-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t.nav.contact}
              </Link>
              <Link 
                href="/admin" 
                className="text-chocolate-700 hover:text-chocolate-900 block px-3 py-2 rounded-md text-base font-medium"
                onClick={() => setIsOpen(false)}
              >
                {t.nav.admin}
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
