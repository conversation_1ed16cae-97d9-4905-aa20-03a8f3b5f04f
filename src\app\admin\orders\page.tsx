'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { fr } from 'date-fns/locale'
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Package, 
  Truck, 
  Eye,
  Phone,
  Mail,
  MapPin
} from 'lucide-react'
import { supabase, type Order } from '@/lib/supabase'

export default function AdminOrdersPage() {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>('all')

  useEffect(() => {
    fetchOrders()
  }, [])

  const fetchOrders = async () => {
    setLoading(true)
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .order('created_at', { ascending: false })

    if (data && !error) {
      setOrders(data)
    }
    setLoading(false)
  }

  const updateOrderStatus = async (orderId: string, newStatus: Order['status']) => {
    const { error } = await supabase
      .from('orders')
      .update({ status: newStatus })
      .eq('id', orderId)

    if (!error) {
      setOrders(orders.map(order => 
        order.id === orderId 
          ? { ...order, status: newStatus }
          : order
      ))
      
      if (selectedOrder && selectedOrder.id === orderId) {
        setSelectedOrder({ ...selectedOrder, status: newStatus })
      }
    }
  }

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'confirmed':
        return <CheckCircle className="h-4 w-4" />
      case 'preparing':
        return <Package className="h-4 w-4" />
      case 'ready':
        return <Package className="h-4 w-4" />
      case 'delivered':
        return <Truck className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'confirmed':
        return 'bg-blue-100 text-blue-800'
      case 'preparing':
        return 'bg-purple-100 text-purple-800'
      case 'ready':
        return 'bg-green-100 text-green-800'
      case 'delivered':
        return 'bg-gray-100 text-gray-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusLabel = (status: Order['status']) => {
    const labels = {
      'pending': 'En attente',
      'confirmed': 'Confirmée',
      'preparing': 'En préparation',
      'ready': 'Prête',
      'delivered': 'Livrée',
      'cancelled': 'Annulée'
    }
    return labels[status] || status
  }

  const filteredOrders = statusFilter === 'all' 
    ? orders 
    : orders.filter(order => order.status === statusFilter)

  if (loading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-rose-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="font-handwriting text-4xl font-bold text-chocolate-800">
            Gestion des Commandes
          </h1>
          <p className="text-chocolate-600 mt-2">
            Suivez et gérez toutes les commandes clients
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filtres */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setStatusFilter('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                statusFilter === 'all'
                  ? 'bg-rose-500 text-white'
                  : 'bg-white text-chocolate-700 hover:bg-rose-100 border border-chocolate-200'
              }`}
            >
              Toutes ({orders.length})
            </button>
            {['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'].map((status) => {
              const count = orders.filter(order => order.status === status).length
              return (
                <button
                  key={status}
                  onClick={() => setStatusFilter(status)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    statusFilter === status
                      ? 'bg-rose-500 text-white'
                      : 'bg-white text-chocolate-700 hover:bg-rose-100 border border-chocolate-200'
                  }`}
                >
                  {getStatusLabel(status as Order['status'])} ({count})
                </button>
              )
            })}
          </div>
        </div>

        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-chocolate-600 text-lg">
              Aucune commande trouvée.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Liste des commandes */}
            <div className="lg:col-span-2">
              <div className="space-y-4">
                {filteredOrders.map((order) => (
                  <div
                    key={order.id}
                    className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all hover:shadow-lg ${
                      selectedOrder?.id === order.id ? 'ring-2 ring-rose-500' : ''
                    }`}
                    onClick={() => setSelectedOrder(order)}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="font-semibold text-chocolate-800">
                          Commande #{order.id.slice(-8)}
                        </h3>
                        <p className="text-sm text-chocolate-600">
                          {order.customer_name}
                        </p>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(order.status)}`}>
                          {getStatusIcon(order.status)}
                          <span className="ml-1">{getStatusLabel(order.status)}</span>
                        </span>
                        <p className="text-sm text-chocolate-600 mt-1">
                          {format(new Date(order.created_at), 'dd MMM yyyy', { locale: fr })}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-chocolate-600">
                          {order.products.length} produit(s)
                        </p>
                        <p className="text-sm text-chocolate-600">
                          Livraison: {format(new Date(order.delivery_date), 'dd MMM yyyy', { locale: fr })}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold text-chocolate-800">
                          {order.total_amount.toFixed(2)}€
                        </p>
                        <button className="text-rose-500 hover:text-rose-600 text-sm flex items-center">
                          <Eye className="h-4 w-4 mr-1" />
                          Voir détails
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Détails de la commande sélectionnée */}
            <div className="lg:col-span-1">
              {selectedOrder ? (
                <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
                  <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                    Détails de la commande
                  </h3>
                  
                  {/* Informations client */}
                  <div className="mb-6">
                    <h4 className="font-medium text-chocolate-800 mb-3">Informations client</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-chocolate-500 mr-2" />
                        <span>{selectedOrder.customer_email}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 text-chocolate-500 mr-2" />
                        <span>{selectedOrder.customer_phone}</span>
                      </div>
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 text-chocolate-500 mr-2 mt-0.5" />
                        <span>{selectedOrder.customer_address}</span>
                      </div>
                    </div>
                  </div>

                  {/* Produits */}
                  <div className="mb-6">
                    <h4 className="font-medium text-chocolate-800 mb-3">Produits commandés</h4>
                    <div className="space-y-2">
                      {selectedOrder.products.map((product, index) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span>{product.product_name} x{product.quantity}</span>
                          <span>{(product.unit_price * product.quantity).toFixed(2)}€</span>
                        </div>
                      ))}
                      <div className="border-t pt-2 font-semibold">
                        <div className="flex justify-between">
                          <span>Total:</span>
                          <span>{selectedOrder.total_amount.toFixed(2)}€</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Notes */}
                  {selectedOrder.notes && (
                    <div className="mb-6">
                      <h4 className="font-medium text-chocolate-800 mb-2">Notes spéciales</h4>
                      <p className="text-sm text-chocolate-600 bg-cream-50 p-3 rounded">
                        {selectedOrder.notes}
                      </p>
                    </div>
                  )}

                  {/* Actions */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-chocolate-800 mb-3">Changer le statut</h4>
                    {['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'].map((status) => (
                      <button
                        key={status}
                        onClick={() => updateOrderStatus(selectedOrder.id, status as Order['status'])}
                        disabled={selectedOrder.status === status}
                        className={`w-full text-left px-3 py-2 rounded text-sm transition-colors ${
                          selectedOrder.status === status
                            ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                            : 'bg-cream-50 text-chocolate-700 hover:bg-cream-100'
                        }`}
                      >
                        <div className="flex items-center">
                          {getStatusIcon(status as Order['status'])}
                          <span className="ml-2">{getStatusLabel(status as Order['status'])}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                  <p className="text-chocolate-600">
                    Sélectionnez une commande pour voir les détails
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
