from django.core.management.base import BaseCommand
from bakery.models import Category, Product, Review, Contact


class Command(BaseCommand):
    help = 'Charge des données d\'exemple pour la boulangerie'

    def handle(self, *args, **options):
        self.stdout.write('🎂 Chargement des données d\'exemple...')

        # <PERSON><PERSON><PERSON> d'abord les catégories
        self.stdout.write('📂 Création des catégories...')
        categories_data = [
            {
                'name': 'gateaux',
                'display_name': 'Gâteaux',
                'description': 'Gâteaux artisanaux, tartes et desserts'
            },
            {
                'name': 'pain',
                'display_name': 'Pains',
                'description': 'Pains frais et spécialités boulangères'
            },
            {
                'name': 'sables',
                'display_name': 'Sablés',
                'description': 'Biscuits sablés et cookies maison'
            },
            {
                'name': 'viennoiseries',
                'display_name': 'Viennoiseries',
                'description': 'Croissants, pains au chocolat et autres viennoiseries'
            },
            {
                'name': 'autres',
                'display_name': 'Autres',
                'description': 'Autres spécialités de la boulangerie'
            }
        ]

        categories = {}
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={
                    'display_name': cat_data['display_name'],
                    'description': cat_data['description'],
                    'active': True
                }
            )
            categories[cat_data['name']] = category
            if created:
                self.stdout.write(f'✅ Catégorie créée: {category.display_name}')
            else:
                self.stdout.write(f'⏭️  Catégorie existante: {category.display_name}')

        # Créer des produits d'exemple
        products_data = [
            {
                'name': 'Gâteau au Chocolat',
                'description': 'Délicieux gâteau au chocolat noir avec ganache maison, préparé avec du chocolat belge de qualité supérieure.',
                'price': 25.00,
                'category': categories['gateaux'],
                'available': True
            },
            {
                'name': 'Pain de Campagne',
                'description': 'Pain artisanal au levain naturel, croûte dorée et mie moelleuse. Cuit au feu de bois selon la tradition.',
                'price': 4.50,
                'category': categories['pain'],
                'available': True
            },
            {
                'name': 'Sablés Vanille',
                'description': 'Sablés fondants à la vanille de Madagascar, par 12 pièces. Parfaits pour accompagner votre thé ou café.',
                'price': 8.00,
                'category': categories['sables'],
                'available': True
            },
            {
                'name': 'Croissant Beurre',
                'description': 'Croissant pur beurre, feuilletage croustillant et doré. Préparé chaque matin avec du beurre français.',
                'price': 1.80,
                'category': categories['viennoiseries'],
                'available': True
            },
            {
                'name': 'Tarte aux Pommes',
                'description': 'Tarte aux pommes fraîches sur pâte brisée maison, avec une pointe de cannelle et de sucre roux.',
                'price': 18.00,
                'category': categories['gateaux'],
                'available': True
            },
            {
                'name': 'Baguette Tradition',
                'description': 'Baguette de tradition française, farine Label Rouge. Croustillante à l\'extérieur, moelleuse à l\'intérieur.',
                'price': 1.20,
                'category': categories['pain'],
                'available': True
            },
            {
                'name': 'Éclair au Café',
                'description': 'Éclair garni d\'une crème pâtissière au café et glacé au fondant café. Un classique revisité.',
                'price': 3.50,
                'category': categories['viennoiseries'],
                'available': True
            },
            {
                'name': 'Cookies Chocolat',
                'description': 'Cookies moelleux aux pépites de chocolat, par 6 pièces. Recette américaine authentique.',
                'price': 6.00,
                'category': categories['sables'],
                'available': True
            }
        ]
        
        for product_data in products_data:
            product, created = Product.objects.get_or_create(
                name=product_data['name'],
                defaults=product_data
            )
            if created:
                self.stdout.write(f'✅ Produit créé: {product.name}')
            else:
                self.stdout.write(f'⏭️  Produit existant: {product.name}')
        
        # Créer des avis d'exemple
        reviews_data = [
            {
                'customer_name': 'Marie Dubois',
                'rating': 5,
                'comment': 'Excellente boulangerie ! Les gâteaux sont délicieux et le service est parfait. Je recommande vivement le gâteau au chocolat.',
                'approved': True
            },
            {
                'customer_name': 'Pierre Martin',
                'rating': 5,
                'comment': 'Le pain de campagne est exceptionnel, je recommande vivement ! La croûte est parfaite et la mie délicieuse.',
                'approved': True
            },
            {
                'customer_name': 'Sophie Laurent',
                'rating': 4,
                'comment': 'Très bons produits, livraison rapide. Juste un petit bémol sur les horaires d\'ouverture qui pourraient être étendus.',
                'approved': True
            },
            {
                'customer_name': 'Jean Dupont',
                'rating': 5,
                'comment': 'Les croissants sont un délice ! On sent la qualité du beurre et le feuilletage est parfait.',
                'approved': True
            }
        ]
        
        for review_data in reviews_data:
            review, created = Review.objects.get_or_create(
                customer_name=review_data['customer_name'],
                defaults=review_data
            )
            if created:
                self.stdout.write(f'✅ Avis créé: {review.customer_name}')
            else:
                self.stdout.write(f'⏭️  Avis existant: {review.customer_name}')
        
        # Créer un message de contact d'exemple
        contact_data = {
            'name': 'Julie Moreau',
            'email': '<EMAIL>',
            'subject': 'Commande spéciale pour anniversaire',
            'message': 'Bonjour, je souhaiterais commander un gâteau personnalisé pour l\'anniversaire de ma fille. Pourriez-vous me contacter pour discuter des détails ? Merci beaucoup !'
        }
        
        contact, created = Contact.objects.get_or_create(
            email=contact_data['email'],
            defaults=contact_data
        )
        if created:
            self.stdout.write(f'✅ Message de contact créé: {contact.name}')
        else:
            self.stdout.write(f'⏭️  Message de contact existant: {contact.name}')
        
        self.stdout.write(
            self.style.SUCCESS('🎉 Données d\'exemple chargées avec succès !')
        )
