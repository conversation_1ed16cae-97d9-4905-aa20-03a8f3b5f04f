import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Navbar } from '@/components/Navbar'
import { Footer } from '@/components/Footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'RBA Kerry - Boulangerie Artisanale',
  description: 'Découvrez nos délicieuses créations artisanales : pains, gâteaux, sablés et bien plus encore. Fait maison avec amour.',
  keywords: 'boulangerie, artisanal, pain, gâteaux, sablés, fait maison, Kerry',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr">
      <body className={inter.className}>
        <div className="min-h-screen flex flex-col">
          <Navbar />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  )
}
