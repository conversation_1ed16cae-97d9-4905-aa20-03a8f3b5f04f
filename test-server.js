const http = require('http');

const server = http.createServer((req, res) => {
    console.log(`Requête reçue: ${req.method} ${req.url}`);
    
    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RBA Kerry - Test Serveur</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        cream: { 50: '#fefdf8', 100: '#fdf9e7', 200: '#faf0c4' },
                        chocolate: { 600: '#a67c4c', 700: '#8a6441', 800: '#70523a' },
                        rose: { 500: '#ec4899', 600: '#db2777' }
                    },
                    fontFamily: {
                        'handwriting': ['Dancing Script', 'cursive'],
                        'serif': ['Playfair Display', 'serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-cream-50">
    <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
            <h1 class="font-handwriting text-6xl font-bold text-chocolate-800 mb-4">
                🎉 RBA Kerry
            </h1>
            <p class="font-serif text-2xl text-chocolate-700 mb-6">
                Serveur Node.js lancé avec succès !
            </p>
            <div class="bg-white rounded-lg shadow-lg p-8 max-w-md mx-auto">
                <h2 class="text-xl font-bold text-chocolate-800 mb-4">✅ Test réussi !</h2>
                <p class="text-chocolate-600 mb-4">
                    Le serveur Node.js fonctionne parfaitement. Vous pouvez maintenant installer les dépendances Next.js.
                </p>
                <div class="bg-cream-100 p-4 rounded-lg text-left text-sm">
                    <p class="font-bold mb-2">Prochaines étapes :</p>
                    <ol class="list-decimal list-inside space-y-1 text-chocolate-700">
                        <li>Arrêter ce serveur (Ctrl+C)</li>
                        <li>Exécuter: npm install</li>
                        <li>Puis: npm run dev</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
    
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(html);
});

const PORT = 3000;
server.listen(PORT, '127.0.0.1', () => {
    console.log(`🚀 Serveur de test lancé sur http://localhost:${PORT}`);
    console.log('📱 Node.js fonctionne correctement !');
    console.log('🌐 Ouverture du navigateur...');
    
    // Ouvrir automatiquement le navigateur
    setTimeout(() => {
        const { exec } = require('child_process');
        exec(`start http://localhost:${PORT}`, (error) => {
            if (error) {
                console.log('⚠️  Ouvrez manuellement: http://localhost:3000');
            }
        });
    }, 1000);
});
