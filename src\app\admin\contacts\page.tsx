'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { fr } from 'date-fns/locale'
import { Mail, Phone, MessageSquare, Trash2, Eye } from 'lucide-react'
import { supabase, type Contact } from '@/lib/supabase'

export default function AdminContactsPage() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchContacts()
  }, [])

  const fetchContacts = async () => {
    setLoading(true)
    const { data, error } = await supabase
      .from('contacts')
      .select('*')
      .order('created_at', { ascending: false })

    if (data && !error) {
      setContacts(data)
    }
    setLoading(false)
  }

  const deleteContact = async (contactId: string) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
      const { error } = await supabase
        .from('contacts')
        .delete()
        .eq('id', contactId)

      if (!error) {
        setContacts(contacts.filter(contact => contact.id !== contactId))
        if (selectedContact?.id === contactId) {
          setSelectedContact(null)
        }
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-cream-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-rose-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-cream-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <h1 className="font-handwriting text-4xl font-bold text-chocolate-800">
            Messages de Contact
          </h1>
          <p className="text-chocolate-600 mt-2">
            Gérez les messages reçus via le formulaire de contact
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {contacts.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="h-16 w-16 text-chocolate-400 mx-auto mb-4" />
            <p className="text-chocolate-600 text-lg">
              Aucun message de contact reçu.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Liste des messages */}
            <div className="lg:col-span-2">
              <div className="space-y-4">
                {contacts.map((contact) => (
                  <div
                    key={contact.id}
                    className={`bg-white rounded-lg shadow-md p-6 cursor-pointer transition-all hover:shadow-lg ${
                      selectedContact?.id === contact.id ? 'ring-2 ring-rose-500' : ''
                    }`}
                    onClick={() => setSelectedContact(contact)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-chocolate-800">
                            {contact.name}
                          </h3>
                          <span className="text-sm text-chocolate-500">
                            {format(new Date(contact.created_at), 'dd MMM yyyy à HH:mm', { locale: fr })}
                          </span>
                        </div>
                        
                        <p className="font-medium text-chocolate-700 mb-2">
                          {contact.subject}
                        </p>
                        
                        <p className="text-chocolate-600 text-sm line-clamp-2">
                          {contact.message}
                        </p>
                        
                        <div className="flex items-center space-x-4 mt-3 text-sm text-chocolate-500">
                          <div className="flex items-center">
                            <Mail className="h-4 w-4 mr-1" />
                            {contact.email}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            setSelectedContact(contact)
                          }}
                          className="text-blue-600 hover:text-blue-800 p-1"
                          title="Voir le détail"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteContact(contact.id)
                          }}
                          className="text-red-600 hover:text-red-800 p-1"
                          title="Supprimer"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Détail du message sélectionné */}
            <div className="lg:col-span-1">
              {selectedContact ? (
                <div className="bg-white rounded-lg shadow-md p-6 sticky top-8">
                  <h3 className="font-serif text-xl font-semibold text-chocolate-800 mb-4">
                    Détail du message
                  </h3>
                  
                  {/* Informations de contact */}
                  <div className="mb-6">
                    <h4 className="font-medium text-chocolate-800 mb-3">Expéditeur</h4>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <MessageSquare className="h-4 w-4 text-chocolate-500 mr-2" />
                        <span className="font-medium">{selectedContact.name}</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 text-chocolate-500 mr-2" />
                        <a 
                          href={`mailto:${selectedContact.email}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {selectedContact.email}
                        </a>
                      </div>
                      <div className="text-sm text-chocolate-500">
                        Reçu le {format(new Date(selectedContact.created_at), 'dd MMMM yyyy à HH:mm', { locale: fr })}
                      </div>
                    </div>
                  </div>

                  {/* Sujet */}
                  <div className="mb-6">
                    <h4 className="font-medium text-chocolate-800 mb-2">Sujet</h4>
                    <p className="text-chocolate-700 bg-cream-50 p-3 rounded">
                      {selectedContact.subject}
                    </p>
                  </div>

                  {/* Message */}
                  <div className="mb-6">
                    <h4 className="font-medium text-chocolate-800 mb-2">Message</h4>
                    <div className="text-chocolate-700 bg-cream-50 p-3 rounded whitespace-pre-wrap">
                      {selectedContact.message}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="space-y-2">
                    <a
                      href={`mailto:${selectedContact.email}?subject=Re: ${selectedContact.subject}`}
                      className="w-full bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                    >
                      <Mail className="h-4 w-4 mr-2" />
                      Répondre par email
                    </a>
                    
                    <button
                      onClick={() => deleteContact(selectedContact.id)}
                      className="w-full bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Supprimer le message
                    </button>
                  </div>
                </div>
              ) : (
                <div className="bg-white rounded-lg shadow-md p-6 text-center">
                  <MessageSquare className="h-12 w-12 text-chocolate-400 mx-auto mb-4" />
                  <p className="text-chocolate-600">
                    Sélectionnez un message pour voir les détails
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
